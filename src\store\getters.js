const getters = {
  userInfo: state => state.user.userInfo,
  lang: state => state.app.lang,
  app: state => state.app,
  logo: state => {
    if (state.app.logo) {
      return state.app.logo
    }
    return require('@/assets/img/logo.png')
  },
  name: state => {
    if (state.app.name) {
      return state.app.name
    }
    return window.WKConfig.companyName
  },
  nowMounth: state => state.app.nowMounth,
  moduleData: state => state.app.moduleData,

  collapse: state => state.app.sidebar.collapse,
  systemAlertShow: state => state.app.systemAlertShow,
  systemAlertProps: state => state.app.systemAlertProps,
  moduleAuth: state => state.app.moduleAuth,
  userList: state => state.user.userList,
  userDeptObj: state => state.user.userDeptObj,
  userDeptMap: state => state.user.userDeptMap,
  searchUserDept: state => state.user.searchUserDept,
  deptList: state => state.user.deptList,

  // 权限
  allAuth: state => state.user.allAuth,
  crm: state => state.user.crm,
  bi: state => state.user.bi,
  manage: state => state.user.manage,
  oa: state => state.user.oa,
  call: state => state.user.call, // 模块权限
  isCall: state => state.crm.isCall, // 当前人权限

  // 路由
  addRouters: state => state.permission.addRouters,
  crmRouters: state => state.permission.crmRouters,
  taskExamineRouters: state => state.permission.taskExamineRouters,
  workLogRouters: state => state.permission.workLogRouters,
  addressBookRouters: state => state.permission.addressBookRouters,
  biRouters: state => state.permission.biRouters,
  manageRouters: state => state.permission.manageRouters,
  oaRouters: state => state.permission.oaRouters,
  fmRouters: state => state.permission.fmRouters,

  // 客户管理信息
  messageNum: state => state.crm.messageNum,
  // 配置信息
  CRMConfig: state => state.app.CRMConfig,
  imageCache: state => state.app.imageCache
}
/**
 * 使用说明
 * import { mapGetters } from 'vuex'
 * computed: {
    ...mapGetters([
      'userInfo'
    ])
  }
 */

export default getters
