// 跟进记录效果
.f-container {
  padding: 0 17px;
  background-color: white;
}

/** 发布行css  */
.se-section {
  position: relative;
  height: 40px;
  margin: 5px 0;
  line-height: 40px;

  .se-name {
    margin-right: 5px;
    font-size: 12px;
    color: $--color-text-primary;
  }

  .se-select {
    height: 25px;
    padding: 0 8px;
    font-size: 12px;
    font-weight: 500;
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 2px;

    .se-select-name {
      margin-right: 20px;
    }
  }

  .se-datepicker {
    width: 200px;
    margin-right: 20px;
    font-size: 12px;
  }

  .se-send {
    position: absolute;
    right: 0;
    padding: 5px 15px;
  }
}

.el-date-editor ::v-deep .el-input__inner {
  height: 25px;
  line-height: 25px;
}

.el-date-editor ::v-deep .el-input__icon {
  height: 25px;
  line-height: 25px;
}

.el-checkbox ::v-deep .el-checkbox__label {
  font-size: 12px;
  color: $--color-text-primary;
}

.log-cont {
  // margin: 30px 0;
  border: 1px solid #e6e6e6;
  border-radius: 2px;

  .log-tabs-item {
    padding: 8px 15px;
    font-size: 13px;
    cursor: pointer;
  }

  .log-tabs-item:hover {
    color: #f18c70 !important;
  }

  .log-tabs-line {
    width: 2px;
    height: 10px;
    background-color: #e6e6e6;
  }
}

.log-items {
  position: relative;
  min-height: 400px;
}
