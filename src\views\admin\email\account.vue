<template>
  <div
    v-loading="loading"
    class="system-customer main">
    <xr-header
      label="邮件账号" />
       <div class="main-body">
    <flexbox class="handle-bar" justify="space-between">
      <flexbox>
        <!-- 人员搜索选择器 -->
        <el-select
          v-model="searchData.userId"
          filterable
          remote
          reserve-keyword
          placeholder="请搜索人员"
          :loading="userSearchLoading"
          clearable
          class="select-item">
          <el-option
            v-for="item in userSearchOptions"
            :key="item.userId"
            :label="item.realname"
            :value="item.userId">
            <span style="float: left">{{ item.realname }}</span>
          </el-option>
          <el-button
            slot="suffix"
            type="icon"
            icon="wk wk-sousuo"
            @click.native="handleUserSearch" />
        </el-select>
        <el-select
          v-model="searchData.type"
          placeholder="请选择账号类型"
          class="select-item">
          <el-option
            v-for="item in [{label:'个人', value:1},{label:'公共', value:2}]"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-select
          v-model="searchData.smtp"
          placeholder="请选择邮箱协议"
          class="select-item">
          <el-option
            v-for="item in [{label:'pop', value:'pop'},{label:'imap', value:'imap'}]"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
        <el-input
          v-model="searchData.emailAddress"
          placeholder="请输入邮箱名"
          class="search-input"
          @keyup.enter.native="handleSearchClick"
          @blur="handleSearchClick">
          <!-- <el-button
            slot="suffix"
            type="icon"
            icon="wk wk-sousuo"
            @click.native="handleSearchClick" /> -->
        </el-input>
        <el-button
          type="primary"
          @click="handleSearchClick('search')">搜索</el-button>
      </flexbox>

      <div
        style="flex-shrink: 0;" v-if="emailFlag.addMailAccount">
        <el-button
        type="primary"
          @click="addViewShow = true">添加邮箱</el-button>
      </div>
    </flexbox>
    <div class="content">
      <el-table
        id="task-set-table"
        :data="mailAccountList"
        :height="tableHeight"
        header-align="center"
        align="center"
        highlight-current-row>
        <el-table-column
          v-for="(item, index) in fieldList"
          :key="index"
          :fixed="index==0"
          :prop="item.field"
          :label="item.name"
          show-overflow-tooltip
          :min-width="item.field === 'emailAddress' ? '180' : '120'">
           <template slot-scope="scope">
           <div v-if="item.field === 'type'">
              {{ scope.row['type'] === 1 ? '个人' : '企业' }}
            </div>
            <div v-else>
              {{ scope.row[item.field] }}
            </div>
            </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="250">
          <template slot-scope="scope">
            <el-button
              type="primary-text"
              style="padding: 0;"
              @click="handleOperateClick('view', scope)">查看</el-button>
            <el-button
              type="primary-text"
              v-if="emailFlag.updateMailAccount"
              style="padding: 0;"
              @click="handleOperateClick('edit', scope)">编辑</el-button>
            <el-button
              type="primary-text"
               v-if="emailFlag.deleteMailAccountById"
              style="padding: 0;"
              @click="handleOperateClick('delete', scope)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="p-contianer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          :total="total"
          class="p-bar"
          background
          layout="prev, pager, next, sizes, total, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>
</div>
    <add-goal
      :visible.sync="addViewShow"
      :type="tabType"
      :emailFlag="emailFlag"
      @success="tabTypeClick" />
      <advanced-email-settings
      @refresh-list="getList"
      :advanced-settings-visible.sync="advancedSettingsVisible"
      :initial-data="rowDetail"
      :operateType="operateType"/>
    <!-- 导入目标 -->
  </div>
</template>

<script>
import { userListAPI } from '@/api/common'
import XrHeader from '@/components/XrHeader'
import AddGoal from './components/AddGoal'
import WkDeptDialogSelect from '@/components/NewCom/WkDeptDialogSelect'
import WkUserDialogSelect from '@/components/NewCom/WkUserDialogSelect'
import { mapGetters } from 'vuex'
import { queryMailAccountPageListAPI, deleteMailAccountByIdAPI } from '@/api/crm/email'
import AdvancedEmailSettings from './components/AdvancedEmailSettings'
export default {
  /** 业绩目标设置 */
  name: 'TaskSetStatistics',
  components: {
    AddGoal,
    AdvancedEmailSettings,
    WkDeptDialogSelect,
    WkUserDialogSelect,
    XrHeader
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      loading: false,
      tableHeight: document.documentElement.clientHeight - 280,
      tabType: 'department',

      dateSelect: '', // 选择的date区间
      typeSelect: '', // 类型选择 1销售（目标）2回款（目标）
      protocolSelect: '', // 邮箱协议选择
      /** 部门选择解析数据 */
      structuresProps: {
        children: 'children',
        label: 'label',
        value: 'id'
      },
      deptList: [], // 部门列表
      structuresSelectValue: '',
      /** 用户列表 */
      userSelectValue: '',
      /** 人员搜索相关 */
      selectedUser: '我的账号', // 选中的用户ID
      userSearchOptions: [
        {
          realname:'我的账号',
          userId:1
        },
        {
          realname:'全公司',
          userId:2
        }
      ], // 搜索到的用户列表
      userSearchLoading: false, // 搜索加载状态
      defaultUserOptions: [], // 缓存的默认用户选项
      searchInput: '', // 邮箱搜索输入框内容
      searchData:{
          type:'',
          smtp:'',
          userId:'',
          emailAddress:''
      },
      /** 编辑控制 */
      isEdit: false, // 是否是编辑中
      currentPage: 1,
      pageSize: 15,
      pageSizes: [15, 30, 45, 60],
      total: 0,
      mailAccountList: [],
      fieldList: [
        { field: 'emailAddress', name: '邮箱账号' },
        { field: 'userName', name: '拥有人' },
        { field: 'type', name: '账号类型' },
        { field: 'smtp', name: '邮箱协议' }
      ],
      emailFlag:{},
      rowDetail:{},
      operateType:'',
      // 设置目标
      addViewShow: false,
      // 导入目标
      importGoalShow: false,
      advancedSettingsVisible:false
    }
  },
  computed: {
    ...mapGetters(['userInfo','crm'])
  },
  mounted() {
    window.onresize = () => {
      this.tableHeight = document.documentElement.clientHeight - 280
    }
    const {emailAccount} = this.crm;
    this.emailFlag = emailAccount;
    this.queryUserList();
    this.getList();
  },
  methods: {
     handleSearchClick() {
        this.getList();
    },
    handleOperateClick(type, scope){
      this.operateType = type;
      if (type === 'view') {
        this.rowDetail = scope.row
        this.advancedSettingsVisible = true 
      } else if (type === 'edit') {
        this.rowDetail = scope.row
        this.advancedSettingsVisible = true
      } else if (type === 'delete') {
        // 启用停用
        this.$confirm('此操作将永久删除, 是否继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            deleteMailAccountByIdAPI({
              id: scope.row['id']
            }).then(res => {
              this.mailAccountList.splice(scope.$index, 1)
              if (this.mailAccountList.length === 0) {
                this.currentPage = this.currentPage - 1 > 0 ? this.currentPage - 1 : 1
              }
              this.getList()
              this.$message({
                type: 'success',
                message: '操作成功'
              })
            }).catch(() => {})
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
      } 
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },

    /**
     * 更改当前页数
     */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    },
    getList() {
      let params = {
        current: this.currentPage,
        pages: this.pageSize,
        condition:{
          type:this.searchData.type,
          smtp:this.searchData.smtp,
          userId:this.searchData.userId === 1 || this.searchData.userId === 2 ? '': this.searchData.userId,
          emailAddress:this.searchData.emailAddress,
          selectType:this.searchData.userId === 1 || this.searchData.userId === 2 ? this.searchData.userId : ''
        }
      };
      // this.loading = true
      queryMailAccountPageListAPI(params)
        .then(res => {
          this.mailAccountList = res.data.records
          this.total = res.data.totalRow
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    queryUserList(){
      userListAPI({
        pageType: 0,
        realname: ''
      }).then(res => {
        this.userSearchOptions = this.userSearchOptions.concat(res.data.list || []);
        this.userSearchLoading = false
      }).catch(() => {
        this.userSearchLoading = false
      })
    },

    /**
     * 用户搜索按钮点击
     */
    handleUserSearch() {
      console.log('点击用户搜索按钮')
      // 这里可以添加搜索按钮的处理逻辑
      // 比如触发搜索或者其他操作
    },

    /**
     * 初始化用户搜索选项
     */
    tabTypeClick() {
      this.getList()
    },

    /** 通过回调控制style */
    cellStyle({ row, column, rowIndex, columnIndex }) {
      // if (rowIndex === this.mailAccountList.length - 1) {
      //   return {
      //     backgroundColor: '#FAF9F6'
      //   }
      // } else if (
      //   columnIndex == 1 ||
      //   columnIndex == 2 ||
      //   columnIndex == 6 ||
      //   columnIndex == 10 ||
      //   columnIndex == 14
      // ) {
      //   return {
      //     backgroundColor: '#E5F4FE',
      //     textAlign: 'center'
      //   }
      // } else {
      //   return { textAlign: 'center' }
      // }
    },

    /**
     * 删除目标
     */
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../styles/index.scss";
.main-body {
  margin-top: #{$--interval-base * 2};
}
.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-x: auto;
}

.handle-bar {
  .el-date-editor {
    width: 130px;
    margin-right: 15px;
  }

  // 统一三个选择器的宽度
  .select-item {
    width: 160px;
    margin-right: 15px;
  }

  .wk-dept-dialog-select,
  .wk-user-dialog-select {
    width: 180px;
    margin-right: 15px;
  }

  .search-input {
    width: 200px;
    margin-right: 15px;
  }
}

.content {
  flex: 1;
  padding: 10px 0;
  overflow-y: hidden;
}

.el-tabs ::v-deep .el-tabs__nav-wrap::after {
  display: none !important;
}

.el-table ::v-deep th {
  text-align: center;
}

.table-show-item {
  height: 34px;
  line-height: 34px;
  text-align: center;
}

.wk-delete {
  cursor: pointer;
  opacity: 0;
}

.wk-delete.is-show {
  opacity: 1;
}

.wk-delete:hover {
  color: $--color-danger;
}
</style>
