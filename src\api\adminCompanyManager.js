import request from '@/utils/request'

/**
 * 域名配置
 * @param {*} data
 */
export function adminCompanyManagerSetDomainAPI(data) {
  return request({
    url: 'adminCompanyManager/setCompanyDoMain',
    method: 'post',
    data: data
  })
}

/**
 * 根据域名查询logo和名称
 * @param {*} data
 */
export function adminCompanyManagerQueryLogoAPI(data) {
  return request({
    url: 'adminCompanyManager/queryCompanyLogoByDomain',
    method: 'post',
    data: data
  })
}
