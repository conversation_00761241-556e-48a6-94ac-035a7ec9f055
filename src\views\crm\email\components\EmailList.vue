<template>
    <!-- 中间邮件列表 -->
      <div class="email-list">
        <!-- 邮件列表加载动画 -->
        <div v-if="listLoading" class="email-loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">加载中...</div>
          </div>
        </div>

        <div class="email-group">

          <div class="email-item"
            v-for="(email, index) in filteredEmails"
            :key="index"
            @click="selectEmail(email)"
            style="padding: 4px 12px;"
            :class="{
              'unread': !email.flagsSeen && email.status === 'inbox',
              'selected': selectedEmail && selectedEmail.id === email.id
            }"
          >
            <!-- 邮件类型图标 -->
            <div class="email-type-icon">
              <img src="../assets/inbox.png" alt="收件箱" class="type-icon" v-if="email.status == 'inbox'"/>
              <img src="../assets/sendbox.png" alt="发件箱" class="type-icon" v-if="email.status == 'sent' || email.status == 'draft'"/>
            </div>

            <!-- 邮件发件人信息 -->
            <div class="email-sender">
              {{ email.receivedAddress }}
              <span class="email-tag" v-if="email.tag">@{{ email.tag }}</span>
            </div>

            <!-- 邮件时间 -->
            <div class="email-time">{{ email.time }}</div>

            <!-- 邮件主题 -->
            <div class="email-subject">
              {{ email.subject || '无主题'}}
              <div class="email-tags" v-if="email.tags && email.tags.length > 0">
                <span
                  v-for="(tagId, tagIndex) in email.tags"
                  :key="tagIndex"
                  class="email-tag-label"
                  :style="{ backgroundColor: getTagColor(tagId) }"
                >
                  {{ getTagName(tagId) }}
                </span>
              </div>
              <div v-if="email.archiveFolder" class="email-archive-folder">
                <folder-icon class="icon-tiny" :style="{ color: getArchiveFolderColor(email.archiveFolder) }" />
                {{ getArchiveFolderName(email.archiveFolder) }}
              </div>
            </div>

            <!-- 邮件操作标签 - 放在发件人信息下方 -->
            <div class="email-actions-wrapper">
              <!-- 星标按钮 -->
              <div class="action-item star-container" @click.stop="toggleStarAndTop(email)">
                <star-icon class="action-icon star-icon" :class="{ 'isStarred': email.isStarred }" />
              </div>

              <!-- 附件图标 -->
              <div class="action-item attachment-container" v-if="email.fileBatchId">
                <paperclip-icon class="action-icon attachment-icon" />
              </div>
              <!-- 发送状态 -->
              <div class="action-item status-container" v-if="email.status == 'sent'">
                <check-circle-icon
                  v-if="email.trackingStatus == 'sent'"
                  class="action-icon status-icon sent"
                />
                <check-circle-icon
                  v-else
                  class="action-icon status-icon delivered"
                />
                <div class="status-tooltip">
                  <template v-if="email.trackingStatus === 'sent'">
                    已发送
                  </template>
                  <template v-else>
                    已送达
                  </template>
                </div>
              </div>
              <!-- 发送状态占位符 -->
              <div class="action-item status-placeholder" v-else></div>

              <!-- 回复状态 -->
              <div class="action-item reply-container" v-if="email.status == 'inbox' || email.status == 'sent'">
                <MessageSquareReply
                  class="action-icon reply-icon"
                  :class="{ 'replied': email.replyStatus, 'not-replied': !email.replyStatus }"
                />
                <div class="status-tooltip">
                  <template v-if="email.replyStatus">
                    已回复
                  </template>
                  <template v-else>
                    未回复
                  </template>
                </div>
              </div>
              <!-- 回复状态占位符 -->
              <div class="action-item reply-placeholder" v-else></div>

              <!-- 查看状态按钮 -->
              <div class="action-item view-container" v-if="email.status == 'sent'">
                <el-tooltip placement="left-start" effect="light" popper-class="custom-tooltip">
                  <!-- tooltip触发元素 -->
                  <eye-icon class="action-icon view-icon" :class="{ 'viewed': email.viewedNum > 0, 'not-viewed': email.viewedNum === 0 }" />
                  <!-- tooltip内容插槽 -->
                  <div slot="content" class="view-status-tooltip-content">
                    <div v-if="email.viewedNum && email.viewedNum > 0">
                      <div class="tooltip-summary">
                        共查看{{ email.viewedNum }}次，
                        最后一次查看：{{ email.lastViewedTime || '' }}&nbsp;&nbsp;{{email.lastViewedIpAddress || ''}}
                         <el-button
                        type="text"
                        size="mini"
                        v-if="!email.showTrackDetailTips"
                        @click="getTrackDetailList(email)"
                        style="margin-top: 8px;">
                        展开
                      </el-button> <el-button
                        type="text"
                        size="mini"
                        v-if="email.showTrackDetailTips"
                        @click="getTrackDetailList(email)"
                        style="margin-top: 8px;">
                        收起
                      </el-button>
                      &nbsp;
                      <span v-if="email.showTrackDetailTips">
                      隐藏国内IP
                      <el-switch
                        v-model="showInternalIp"
                        @change="handleShowInternalIpChange"
                        active-color="#13ce66"
                        inactive-color="#ff4949">
                      </el-switch>
                      </span>
                      </div>
                      <div v-if="email.showTrackDetailTips && trackDetailList && trackDetailList.length > 0" class="view-records-table" style="margin-top: 12px;">
                        <div class="table-scroll-container">
                          <el-table
                              :data="filteredTrackDetailList"
                              size="mini"
                              class="detail-table"
                              :height="filteredTrackDetailList.length > 5 ? 250 : null"
                              :show-header=false
                              :border=false
                              stripe
                              empty-text="暂无查看记录">
                              <el-table-column label="IP地址" prop="viewedIp" min-width="120" show-overflow-tooltip>
                                  <template slot-scope="scope">
                                      <span class="ip-text">{{ scope.row.viewedIp || '-' }}</span>
                                  </template>
                              </el-table-column>
                              <el-table-column label="查看时间" prop="viewedTime" min-width="140" show-overflow-tooltip>
                                  <template slot-scope="scope">
                                      <span class="time-text">{{ scope.row.viewedTime || '-'}}</span>
                                  </template>
                              </el-table-column>
                              <el-table-column label="地理位置" prop="location" min-width="180" show-overflow-tooltip>
                                  <template slot-scope="scope">
                                      <span class="location-text">{{ [scope.row.viewedCountry, scope.row.viewedProvince, scope.row.viewedCity].filter(item => item).join(' - ') || '-' }}</span>
                                  </template>
                              </el-table-column>
                          </el-table>
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      该邮件未被收件人查看过
                    </div>
                  </div>
                </el-tooltip>
              </div>
              <!-- 查看状态占位符 -->
              <div class="action-item view-placeholder" v-else></div>
            </div>
          </div>

          <div v-if="filteredEmails.length === 0" class="no-results">
            没有找到匹配的邮件
          </div>
        </div>
        <div class="email-pagination">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            :total="total"
            class="p-bar"
            background
            layout="prev, pager, next, sizes, total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
        </div>
      </div>
</template>
<script>
import {
  ChevronDown, ChevronRight, Search, XCircle, Filter, Sliders,
  Star, Paperclip, CheckCircle, Eye, MessageSquareReply,
  Folder, X
} from 'lucide-vue'
import DropdownMenu from './DropdownMenu.vue'
import DropdownItem from './DropdownItem.vue'
import {
  getCustomerMailsAPI,
  getEmailtrackDetailAPI
} from '@/api/crm/email'
import { formatDateTime } from '@/utils/format'

export default {
  name: 'EmailList',
  components: {
    ChevronDownIcon: ChevronDown,
    ChevronRightIcon: ChevronRight,
    SearchIcon: Search,
    XCircleIcon: XCircle,
    FilterIcon: Filter,
    SlidersIcon: Sliders,
    StarIcon: Star,
    PaperclipIcon: Paperclip,
    CheckCircleIcon: CheckCircle,
    EyeIcon: Eye,
    MessageSquareReply,
    FolderIcon: Folder,
    XIcon: X,
    DropdownMenu,
    DropdownItem
  },
  props: {
    // 当前选中的文件夹
    activeFolder: {
      type: String,
      default: 'inbox'
    },
    // 当前选中的筛选条件
    activeFilter: {
      type: String,
      default: null
    },
    // 当前选中的标签
    activeTag: {
      type: [String, Number],
      default: null
    },
    // 当前选中的归档文件夹
    activeArchiveFolder: {
      type: [String, Number],
      default: null
    },
    // 用户信息
    userInfo: {
      type: Object,
      default: () => ({})
    },
    emailList: {
      type: Array,
      default: () => []
    },
    // 标签列表
    tags: {
      type: Array,
      default: () => []
    },
    // 归档文件夹列表
    archiveFolders: {
      type: Array,
      default: () => []
    },
    // 是否显示追踪详情
    showTrackDetail: {
      type: Boolean,
      default: false
    },
    // 查询条件（从父组件传入）
    queryCondition: {
      type: Object,
      default: () => ({})
    },
    // 是否自动加载数据
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 分页信息（由父组件传入）
    pagination: {
      type: Object,
      default: null
    }
  },
  computed: {
    // 过滤后的邮件列表
    filteredEmails() {
      // 如果父组件传入了邮件列表，优先使用父组件的数据
      if (this.emailList && this.emailList.length > 0) {
        console.log('使用父组件传入的邮件数据:', this.emailList.length, '条')
        return this.emailList
      }
      // 否则使用组件内部加载的数据
      console.log('使用组件内部邮件数据:', this.emails.length, '条')
      return this.emails || []
    },

    // 获取标签名称
    getTagName() {
      return (tagId) => {
        const tag = this.tags.find(t => t.id === tagId)
        return tag ? tag.name : '未知标签'
      }
    },

    // 获取标签颜色
    getTagColor() {
      return (tagId) => {
        const tag = this.tags.find(t => t.id === tagId)
        return tag ? tag.color : '#999'
      }
    },

    // 获取归档文件夹名称
    getArchiveFolderName() {
      return (folderId) => {
        const folder = this.archiveFolders.find(f => f.id === folderId)
        return folder ? folder.name : '未知文件夹'
      }
    },

    // 获取归档文件夹颜色
    getArchiveFolderColor() {
      return (folderId) => {
        const folder = this.archiveFolders.find(f => f.id === folderId)
        return folder ? folder.color : '#999'
      }
    },

    // 分页相关计算属性
    currentPage: {
      get() {
        return this.pagination ? this.pagination.currentPage : this.internalCurrentPage
      },
      set(value) {
        if (this.pagination) {
          this.$emit('page-change', { currentPage: value, pageSize: this.pageSize })
        } else {
          this.internalCurrentPage = value
          this.loadEmailData()
        }
      }
    },

    pageSize: {
      get() {
        return this.pagination ? this.pagination.pageSize : this.internalPageSize
      },
      set(value) {
        if (this.pagination) {
          this.$emit('page-change', { currentPage: 1, pageSize: value })
        } else {
          this.internalPageSize = value
          this.internalCurrentPage = 1
          this.loadEmailData()
        }
      }
    },

    total() {
      return this.pagination ? this.pagination.total : this.internalTotal
    }
  },
  data() {
    return {
      // 邮件列表数据
      emails: [],
      selectedEmail: null,

      // 搜索相关
      searchData: {
        term: '',
        isSearchActive: false,
        showAdvanced: false
      },
      searchDropdown: {
        visible: false,
        highlightedIndex: -1
      },
      searchOptions: [
        { label: '发件人包含', value: 1 },
        { label: '收件人包含', value: 2 },
        { label: '收件人(含抄送)包含', value: 3 },
        { label: '标题包含', value: 4 },
        { label: '内容包含', value: 5 },
        { label: '附件名包含', value: 6 }
      ],
      currentOption: 0,

      // 筛选和排序
      quickFilter: '',
      sortOption: 'default',

      // 内部分页（当父组件未传入pagination时使用）
      internalCurrentPage: 1,
      internalPageSize: 50,
      internalTotal: 0,
      pageSizes: [20, 50, 100, 200],

      // 加载状态
      listLoading: false,

      // 追踪详情相关
      trackDetailList: [],
      filteredTrackDetailList: [],
      showInternalIp: false,

      // 其他状态
      sendType: null,
      isStarred: ''
    }
  },
  methods: {
    formatDateTime,
    // 选择邮件
    selectEmail(email) {
      this.selectedEmail = email;
      this.$emit('email-selected', email);
    },

    // 处理内部IP显示切换
    handleShowInternalIpChange() {
      if (this.showInternalIp) {
        this.filteredTrackDetailList = this.trackDetailList.filter(item => !item.viewedCountry.includes('中国'));
      } else {
        this.filteredTrackDetailList = this.trackDetailList;
      }
    },

    // 分页大小变化处理
    handleSizeChange(newSize) {
      console.log('分页大小变化:', newSize)
      this.pageSize = newSize
    },

    // 当前页码变化处理
    handleCurrentChange(newPage) {
      console.log('当前页码变化:', newPage)
      this.currentPage = newPage
    },

    // 加载邮件数据
    async loadEmailData() {
      if (this.listLoading) {
        console.log('正在加载中，跳过本次请求')
        return
      }

      console.log('开始加载邮件数据')
      this.listLoading = true

      try {
        // 构建查询参数
        const params = {
          current: this.pagination ? this.pagination.currentPage : this.internalCurrentPage,
          size: this.pagination ? this.pagination.pageSize : this.internalPageSize,
          condition: {
            // 合并父组件传入的查询条件
            ...this.queryCondition
          }
        }

        console.log('邮件列表查询参数:', params)

        // 调用API获取数据
        const response = await getCustomerMailsAPI(params)
        console.log('邮件列表获取成功:', response.data)

        // 更新数据
        this.emails = response.data.records || []
        if (!this.pagination) {
          // 只有在组件内部管理分页时才更新内部total
          this.internalTotal = parseInt(response.data.total) || 0
        }

        // 发送数据更新事件
        this.$emit('data-loaded', {
          emails: this.emails,
          total: parseInt(response.data.total) || 0,
          currentPage: this.pagination ? this.pagination.currentPage : this.internalCurrentPage,
          pageSize: this.pagination ? this.pagination.pageSize : this.internalPageSize
        })

        console.log(`邮件列表加载完成，共 ${this.emails.length} 条数据，总计 ${this.total} 条`)

      } catch (error) {
        console.error('获取邮件列表失败:', error)
        this.$message.error('获取邮件列表失败，请稍后重试')

        // 清空数据
        this.emails = []
        if (!this.pagination) {
          this.internalTotal = 0
        }

        // 发送错误事件
        this.$emit('load-error', error)

      } finally {
        this.listLoading = false
      }
    },

    // 刷新数据
    refreshData() {
      console.log('刷新邮件列表数据')
      if (this.pagination) {
        this.$emit('page-change', { currentPage: 1, pageSize: this.pagination.pageSize })
      } else {
        this.internalCurrentPage = 1
        this.loadEmailData()
      }
    },

    // 重置分页
    resetPagination() {
      if (!this.pagination) {
        this.internalCurrentPage = 1
        this.internalPageSize = 50
        this.internalTotal = 0
      }
    },

    // 获取追踪详情列表
    getTrackDetailList(email) {
      // 切换显示状态
      email.showTrackDetailTips = !email.showTrackDetailTips

      if (email.showTrackDetailTips) {
        // 清空之前的数据
        this.trackDetailList = []
        this.filteredTrackDetailList = []

        // 构建查询参数
        const params = {
          condition: {
            emailId: email.emailId
          },
          current: 1,
          size: 50
        }

        // 调用API获取追踪详情
        getEmailtrackDetailAPI(params)
          .then(res => {
            const { records } = res.data
            this.trackDetailList = records || []
            this.filteredTrackDetailList = this.trackDetailList
            console.log('获取邮件追踪详情成功:', this.trackDetailList.length, '条')
          })
          .catch((error) => {
            console.error('获取邮件追踪详情失败:', error)
            this.$message.error('获取邮件追踪详情失败，请稍后重试')
            // 出错时清空数据
            this.trackDetailList = []
            this.filteredTrackDetailList = []
          })
      }
    },

    // 切换星标和置顶
    toggleStarAndTop(email) {
      email.isStarred = !email.isStarred
      // 这里可以调用API更新星标状态
      console.log('切换星标状态:', email.isStarred)
    }
  },

  // 生命周期钩子
  mounted() {
    console.log('EmailList 组件已挂载')
    if (this.autoLoad) {
      console.log('自动加载邮件数据')
      this.loadEmailData()
    }
  },

  // 监听器
  watch: {
    // 监听查询条件变化
    queryCondition: {
      handler(newCondition, oldCondition) {
        console.log('查询条件变化:', { newCondition, oldCondition })
        if (this.autoLoad) {
          this.refreshData()
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped>
/* 引入邮箱样式 */
@import '../styles/index.css';

/* 邮件列表特定样式 */
.email-list {
  flex: 0.5;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6e9ed;
  min-height: 0; /* 允许flex子项收缩 */
  height: calc(100vh - 120px); /* 优化高度，减少预留空间 */
  max-height: calc(100vh - 120px); /* 限制最大高度为视口高度 */
  position: relative;
}

/* 邮件组容器 - 可滚动区域 */
.email-group {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: calc(100% - 80px); /* 减去分页区域高度 */
  max-height: calc(100% - 80px); /* 确保为分页预留足够空间 */
  /* 滚动条样式优化 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* Webkit浏览器滚动条样式 */
.email-group::-webkit-scrollbar {
  width: 6px;
}

.email-group::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.email-group::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.email-group::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 分页容器 - 固定在底部 */
.email-pagination {
  flex-shrink: 0;
  padding: 10px 16px;
  border-top: 1px solid #e6e9ed;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px; /* 固定高度 */
  min-height: 70px;
  max-height: 70px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  position: sticky;
  bottom: 0;
  z-index: 100;
  /* 确保在所有情况下都可见 */
  margin-top: auto;
}

/* 分页组件样式优化 */
.email-pagination :deep(.el-pagination) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 无结果提示样式 */
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #909399;
  font-size: 14px;
  text-align: center;
}

.no-results::before {
  content: '📧';
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.email-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0052cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 14px;
}

/* 搜索下拉框样式 */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e6e9ed;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-header {
  padding: 8px 12px;
  font-size: 12px;
  color: #666;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e6e9ed;
}

.dropdown-option {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.dropdown-option:hover,
.dropdown-option.highlighted {
  background-color: #f0f7ff;
  color: #0052cc;
}

/* 邮件项样式增强 */
.email-item {
  position: relative;
  transition: all 0.2s ease;
}

.email-item:hover {
  background-color: #f8f9fa;
}

.email-item.selected {
  background-color: #e3f2fd;
  border-left: 3px solid #0052cc;
}

.email-item.unread {
  background-color: #fff;
  font-weight: 500;
}

.email-item.unread .email-subject {
  font-weight: 600;
}

/* 查看状态图标样式 */
.view-status .icon-small.viewed {
  color: red;
}

.view-status .icon-small:not(.viewed) {
  color: #ccc;
}

/* 星标图标样式 */
.star-icon.isStarred {
  color: #ffc107;
  fill: #ffc107;
}

.star-icon:not(.isStarred) {
  color: #ccc;
}

.star-icon:hover {
  color: #ffc107;
}

/* 邮件标签样式 */
.email-tag-label {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  color: white;
  margin-right: 4px;
  margin-top: 2px;
}

/* 归档文件夹样式 */
.email-archive-folder {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

/* 邮件项布局优化 - 新的Grid布局 */
.email-item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto auto auto;
  grid-template-areas:
    "type sender time"
    "type subject subject"
    "type actions actions";
  gap: 4px;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: start;
}

/* 邮件类型图标区域 */
.email-type-icon {
  grid-area: type;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.type-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

/* 发件人区域 */
.email-sender {
  grid-area: sender;
  font-weight: 500;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
  align-self: center;
}

/* 时间区域 */
.email-time {
  grid-area: time;
  font-size: 12px;
  color: #999;
  text-align: right;
  white-space: nowrap;
  align-self: center;
}

/* 主题区域 */
.email-subject {
  grid-area: subject;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-top: 2px;
}

/* 操作标签区域 - 放在发件人下方 */
.email-actions-wrapper {
  grid-area: actions;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  flex-wrap: nowrap;
}

/* 操作项统一样式 */
.action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
}

/* 操作图标统一尺寸 */
.action-icon {
  width: 14px;
  height: 14px;
  transition: all 0.2s ease;
}

/* 占位符样式 - 保持布局一致性 */
.attachment-placeholder,
.status-placeholder,
.reply-placeholder,
.view-placeholder {
  width: 20px;
  height: 20px;
  visibility: hidden;
}

/* 星标容器样式 */
.star-container:hover {
  background-color: rgba(255, 193, 7, 0.1);
}

.star-icon {
  color: #d9d9d9;
}

.star-icon:hover {
  color: #ffc107;
}

.star-icon.isStarred {
  color: #ffc107;
  fill: #ffc107;
}

/* 附件图标样式 */
.attachment-container:hover {
  background-color: rgba(0, 82, 204, 0.1);
}

.attachment-icon {
  color: #666;
}

.attachment-container:hover .attachment-icon {
  color: #0052cc;
}

/* 发送状态样式 */
.status-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.status-icon.sent {
  color: #ff9800;
}

.status-icon.delivered {
  color: #4caf50;
}

/* 回复状态样式 */
.reply-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.reply-icon.replied {
  color: #4caf50;
}

.reply-icon.not-replied {
  color: #999;
}

/* 查看状态样式 */
.view-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.view-icon.viewed {
  color: #f44336;
}

.view-icon.not-viewed {
  color: #ccc;
}

/* 状态提示样式 */
.status-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  margin-bottom: 4px;
  pointer-events: none;
}

.action-item:hover .status-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 保持原有的 view-status-tooltip 样式 */
.view-status-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 1000;
  margin-bottom: 4px;
}

.view-status:hover .view-status-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 适应不同容器高度 */
.email-list[data-container="full-height"] {
  height: 100vh;
  max-height: 100vh;
}

.email-list[data-container="auto-height"] {
  height: auto;
  max-height: calc(100vh - 40px);
}

/* 当父容器有固定高度时 */
.email-list[data-parent-height] {
  height: 100%;
  max-height: 100%;
}

.email-list[data-parent-height] .email-group {
  max-height: calc(100% - 80px); /* 减去分页高度 */
}

/* 确保在小屏幕上也能正常显示 */
@media (max-height: 600px) {
  .email-list {
    height: calc(100vh - 80px);
    max-height: calc(100vh - 80px);
  }

  .email-group {
    height: calc(100% - 60px);
    max-height: calc(100% - 60px);
  }

  .email-pagination {
    height: 60px;
    min-height: 60px;
    max-height: 60px;
    padding: 8px 12px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-list {
    flex: 1;
    height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
  }

  .email-group {
    height: calc(100% - 60px);
    max-height: calc(100% - 60px);
  }

  .email-filter {
    flex-direction: column;
    gap: 8px;
  }

  .search-container {
    order: -1;
  }

  /* 移动端分页优化 */
  .email-pagination {
    padding: 8px 12px;
    height: 60px;
    min-height: 60px;
    max-height: 60px;
  }

  .email-pagination :deep(.el-pagination) {
    font-size: 12px;
  }

  .email-pagination :deep(.el-pagination .el-pager li) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }

  .email-pagination :deep(.el-pagination .btn-prev),
  .email-pagination :deep(.el-pagination .btn-next) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }

  /* 移动端邮件项优化 */
  .email-item {
    grid-template-columns: auto 1fr auto;
    gap: 3px 8px;
    padding: 8px;
  }

  .email-type-icon {
    width: 20px;
    height: 20px;
  }

  .type-icon {
    width: 16px;
    height: 16px;
  }

  .email-sender {
    font-size: 13px;
  }

  .email-time {
    font-size: 11px;
  }

  .email-subject {
    font-size: 12px;
  }

  .email-actions-wrapper {
    gap: 4px;
    min-height: 20px;
    margin-top: 2px;
  }

  .action-item {
    width: 18px;
    height: 18px;
  }

  .action-icon {
    width: 12px;
    height: 12px;
  }

  .attachment-placeholder,
  .status-placeholder,
  .reply-placeholder,
  .view-placeholder {
    width: 18px;
    height: 18px;
  }

  /* 移动端邮件组优化 */
  .email-group {
    padding: 4px 0;
  }

  .no-results {
    padding: 40px 20px;
  }

  .no-results::before {
    font-size: 36px;
    margin-bottom: 12px;
  }
}
</style>