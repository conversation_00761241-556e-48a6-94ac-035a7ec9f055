.main-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: #{$--interval-base * 3} 0 0 #{$--interval-base * 5};
  overflow-y: auto;
  background-color: white;
}

.filtrate-bar {
  padding: 0;
  background-color: white;
}

.content {
  height: calc(100% - 54px);
  padding-bottom: #{$--interval-base * 3};
  margin-top: #{$--interval-base * 3};
  overflow-y: auto;
}

.content-title {
  padding: $--interval-base #{$--interval-base * 5};
  font-size: 16px;
}

.axis-content {
  position: relative;
  margin-right: #{$--interval-base * 5};
  margin-bottom: #{$--interval-base * 3};

  .axismain,
  #axismain {
    width: 100%;
    height: 400px;
    margin: 0 auto;
  }
}

.table-content {
  padding-right: #{$--interval-base * 5};
}

.export-button {
  float: right;
  margin-right: 22px;
}

// 导出操作bug
.handle-bar {
  margin-bottom: 8px;
  text-align: right;
}

// ---------进销存------------------
.search-box {
  padding-right: 15px;
}

.radio-box {
  padding: 0 20px;
  margin-bottom: 15px;

  ::v-deep .el-radio__label {
    color: #99a9bf !important;
  }
}

.static {
  padding: 0 20px;
  margin-bottom: 10px;

  .text {
    display: inline-block;
    font-size: 13px;
    color: #99a9bf;
  }

  .text:not(:last-child) {
    margin-right: 15px;
  }
}

.jxc-content {
  padding-bottom: 0;
}

::v-deep .el-table__footer {
  td {
    height: 40px;
    padding: 0;
  }
}
