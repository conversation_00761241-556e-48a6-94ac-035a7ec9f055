<template>
<div class="tracking-detail-container">
    <div class="tracking-header">
        <div class="des-subject">发件追踪-查看邮件<div class="des-text">(仅显示最近1年的数据)</div>
        </div>
        <el-input v-model="searchValue" placeholder="请输入主题/发件人/收件人" class="search-input" @keyup.enter.native="handleSearchClick" @blur="handleSearchClick">
            <el-button slot="suffix" type="icon" icon="wk wk-sousuo" @click.native="handleSearchClick" />
        </el-input>
    </div>

    <div class="tracking-table-wrapper">
        <el-table
            v-loading="loading"
            :data="trackList"
            class="tracking-table"
            highlight-current-row
            :height="tableHeight"
            style="width: 100%;"
            empty-text="暂无追踪记录"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column label="查看人" prop="recipient" show-overflow-tooltip width="220">
                <template slot-scope="scope">
                    <div class="status-badge">
                        <el-button type="info" style="background-color: #909399;" size="mini"  v-if="!scope.row.customerName" round>陌生</el-button>
                        <el-button type="warning" size="mini" v-else round>客户</el-button>
                        {{ scope.row.emailAddress }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="客户名称" prop="customerName" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div class="status-badge">{{ scope.row.customerName || '-' }}</div>
                </template>
            </el-table-column>
            <el-table-column label="邮件主题" prop="subject" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div class="status-badge">{{ scope.row.subject || '-'}}</div>
                </template>
            </el-table-column>

            <el-table-column label="发件时间" prop="sentTime" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div class="status-badge">{{ scope.row.sentTime || '-' }}</div>
                </template>
            </el-table-column>
            <el-table-column label="查看次数" prop="viewedNum" show-overflow-tooltip width="80">
                <template slot-scope="scope">
                    <div class="view-count-container" @mouseleave="handleTooltipMouseLeave">
                        <div
                            class="time-text view-count-trigger"
                            @click="handleViewCountClick(scope.row, $event)">
                            {{ scope.row.viewedNum }}
                        </div>
                        <div
                            v-if="showTooltip && currentTooltipRow && currentTooltipRow.emailId === scope.row.emailId"
                            class="view-status-tooltip"
                            :style="tooltipStyle"
                            @mouseenter="handleTooltipMouseEnter"
                            @mouseleave="handleTooltipMouseLeave">
                            <div v-if="scope.row.viewedNum && scope.row.viewedNum > 0">
                                <div class="tooltip-summary">
                                    共查看{{ scope.row.viewedNum }}次，最后一次查看：{{ scope.row.lastViewedTime || '' }}
                                     &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;隐藏国内IP
                                    <el-switch
                                        v-model="showInternalIp"
                                        @change="handleShowInternalIpChange"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949">
                                    </el-switch><br>
                                    查看地址：{{ scope.row.lastViewedIpAddress || '' }}
                                </div>
                                <div v-if="detailLoading" class="loading-text">
                                    <i class="el-icon-loading"></i> 加载中...
                                </div>
                                <div v-else-if="trackDetailList && trackDetailList.length > 0" class="view-records-table">
                                    <div class="table-scroll-container">
                                        <el-table
                                            :data="filteredTrackDetailList"
                                            size="mini"
                                            :show-header=false
                                            :border=false
                                            :height="filteredTrackDetailList.length > 5 ? 250 : null"
                                            empty-text="暂无查看记录"
                                            class="detail-table">
                                            <el-table-column label="IP地址" prop="viewedIp"  show-overflow-tooltip>
                                                <template slot-scope="detailScope">
                                                    <span class="ip-text">{{ detailScope.row.viewedIp || '-' }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="查看时间" prop="viewedTime"  show-overflow-tooltip>
                                                <template slot-scope="detailScope">
                                                    <span class="time-text">{{ detailScope.row.viewedTime || '-' }}</span>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="地理位置" prop="location"  show-overflow-tooltip>
                                                <template slot-scope="detailScope">
                                                    <span class="location-text">
                                                        <template >
                                                            {{ detailScope.row.viewedAddress || '-' }}
                                                        </template>
                                                    </span>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </div>
                                </div>
                                <div v-else-if="!detailLoading" class="no-detail-text">
                                    暂无详细查看记录
                                </div>
                            </div>
                            <div v-else>
                                该邮件未被收件人查看过
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="最后一次查看时间" prop="lastViewedTime" show-overflow-tooltip>
                <template slot-scope="scope">
                    <div class="status-badge">{{ scope.row.lastViewedTime || '-'}}</div>
                </template>
            </el-table-column>

            <el-table-column label="最后一次查看IP地址" prop="lastViewedIp" align="center">
                <template slot-scope="scope">
                    <span class="status-badge">{{ scope.row.lastViewedIp || '-'}}</span>
                </template>
            </el-table-column>
            <el-table-column label="邮件类型" prop="sendType" align="center" width="100">
                <template slot-scope="scope">
                    <span class="status-badge">{{ getStatusText(scope.row.sendType) || '-' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="发件人" prop="sendEmailAddress" align="center">
                <template slot-scope="scope">
                    <span class="status-badge">{{ scope.row.sendEmailAddress || '-' }}</span>
                </template>
            </el-table-column>
        </el-table>
    </div>

    <div class="pagination-wrapper">
        <el-pagination
            :current-page="currentPage"
            :page-sizes="pageSizes"
            :page-size.sync="pageSize"
            :total="total"
            class="p-bar"
            background
            layout="prev, pager, next, sizes, total, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
    </div>
</div>
</template>


<script>
import {
    getEmailtrackListAPI,
    getEmailtrackDetailAPI
} from '@/api/crm/email'
import { formatDateTime } from '@/utils/format'
export default {
    data() {
        return {
            loading: false,
            searchValue: '',
            multipleSelection: [],
            trackList: [],
            currentPage: 1,
            pageSize: 15,
            pageSizes: [15, 30, 45, 60, 100],
            total: 0,
            trackDetailList: [],
            showTooltip: false,
            currentTooltipRow: null,
            detailLoading: false,
            tooltipStyle: {},
            tooltipTimer: null,
            showInternalIp: false,
            filteredTrackDetailList: [],
            tableHeight: 'auto'
        }
    },
    mounted() {
        this.calculateTableHeight();
        window.addEventListener('resize', this.calculateTableHeight);
    },
    beforeDestroy() {
        // 清理定时器，防止内存泄漏
        if (this.tooltipTimer) {
            clearTimeout(this.tooltipTimer);
            this.tooltipTimer = null;
        }
        // 移除窗口大小变化监听器
        window.removeEventListener('resize', this.calculateTableHeight);
    },
    created() {
        this.getTrackList();
    },
    methods: {
        calculateTableHeight() {
            this.$nextTick(() => {
                try {
                    const container = this.$el;
                    if (!container) return;

                    // 获取容器高度
                    const containerHeight = window.innerHeight;
                    const header = container.querySelector('.tracking-header');
                    const pagination = container.querySelector('.pagination-wrapper');

                    const headerHeight = header ? header.offsetHeight + 20 : 80; // 包含 margin
                    const paginationHeight = pagination ? pagination.offsetHeight + 30 : 80; // 包含 padding
                    const containerPadding = 40; // 容器内边距
                    const extraMargin = 100; // 额外的边距和空间

                    const availableHeight = containerHeight - headerHeight - paginationHeight - containerPadding - extraMargin;
                    this.tableHeight = Math.max(400, Math.min(800, availableHeight)); // 最小400px，最大800px
                } catch (error) {
                    console.warn('计算表格高度失败:', error);
                    this.tableHeight = 500; // 默认高度
                }
            });
        },
        handleSearchClick() {
          this.getTrackList();
        },
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },
        handleSizeChange(val) {
            this.pageSize = val;
            this.getTrackList();
        },
        /**
         * 更改当前页数
         */
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getTrackList();
        },
        handleShowInternalIpChange() {
            if (this.showInternalIp) {
                this.filteredTrackDetailList = this.trackDetailList.filter(item => !item.viewedCountry.includes('中国'));
            } else {
                this.filteredTrackDetailList = this.trackDetailList;
            }
        },
        getTrackList() {
            const params = {
                current: this.currentPage,
                size: this.pageSize,
                condition: {
                    keywords: this.searchValue
                }
            };
            this.loading = true;
            getEmailtrackListAPI(params)
                .then(res => {
                    const { records } = res.data;
                    this.trackList = records;
                    this.total = parseInt(res.data.total);
                    this.loading = false;
                    // 数据加载完成后重新计算表格高度
                    this.$nextTick(() => {
                        this.calculateTableHeight();
                    });
                })
                .catch(() => {
                    this.loading = false;
                });
        },
        getStatusText(type) {
            switch (type) {
                case 1:
                    return '普通邮件';
                case 2:
                    return '一对一邮件';
            }
        },
        handleViewCountClick(row, event) {
            if (row.viewedNum <= 0) {
                return;
            }

            // 如果点击的是同一行且tooltip已显示，则关闭
            if (this.showTooltip && this.currentTooltipRow && this.currentTooltipRow.emailId === row.emailId) {
                this.hideTooltip();
                return;
            }

            // 设置tooltip位置
            this.setTooltipPosition(event);

            // 显示tooltip并加载数据
            this.currentTooltipRow = row;
            this.showTooltip = true;
            this.getTrackDetailList(row);
        },

        setTooltipPosition(event) {
            const rect = event.target.getBoundingClientRect();
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            this.tooltipStyle = {
                position: 'fixed',
                top: (rect.bottom + 8) + 'px',
                left: (rect.left - 100) + 'px',
                zIndex: 9999
            };
        },

        handleTooltipMouseEnter() {
            // 鼠标进入tooltip时清除关闭定时器
            if (this.tooltipTimer) {
                clearTimeout(this.tooltipTimer);
                this.tooltipTimer = null;
            }
        },

        handleTooltipMouseLeave() {
            // 鼠标离开时延迟关闭tooltip
            this.tooltipTimer = setTimeout(() => {
                this.hideTooltip();
            }, 200);
        },

        hideTooltip() {
            this.showTooltip = false;
            this.currentTooltipRow = null;
            this.trackDetailList = [];
            this.detailLoading = false;
            if (this.tooltipTimer) {
                clearTimeout(this.tooltipTimer);
                this.tooltipTimer = null;
            }
        },

        getTrackDetailList(row) {
            const params = {
                condition: {
                    emailId: row.emailId
                },
                current: 1,
                size: 50
            };

            this.detailLoading = true;
            this.trackDetailList = [];

            getEmailtrackDetailAPI(params)
                .then(res => {
                    this.detailLoading = false;
                    const { records } = res.data;
                    this.trackDetailList = records || [];
                    this.filteredTrackDetailList = this.trackDetailList;
                })
                .catch((error) => {
                    this.detailLoading = false;
                    this.trackDetailList = [];
                    this.filteredTrackDetailList = [];
                    console.error('获取邮件查看详情失败:', error);
                });
        }
    }
};
</script>
<style scoped>
/* 查看次数容器 */
.view-count-container {
  position: relative;
  display: inline-block;
}

/* 查看次数触发器 */
.view-count-trigger {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  user-select: none;
  color: #0052cc !important;
}

/* 自定义tooltip样式 */
.view-status-tooltip {
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  max-width: 450px;
  min-width: 300px;
  padding: 12px;
  word-wrap: break-word;
  z-index: 2000;
}

.view-status-tooltip::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #e4e7ed;
}

.view-status-tooltip::after {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fff;
}

/* tooltip内容样式 */
.tooltip-summary {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  color: #303133;
  font-size: 12px;
  line-height: 1.5;
}

.loading-text {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}

.loading-text i {
  margin-right: 5px;
}

.no-detail-text {
  text-align: center;
  color: #909399;
  padding: 20px 0;
  font-size: 12px;
}

.view-records-table {
   margin-top: 8px;
 }

 /* 表格滚动容器 */
 .table-scroll-container {
   width: 100%;
   overflow: hidden;
   border-radius: 4px;
   box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
 }

 /* 详情表格样式 */
 .detail-table {
   width: 100%;
 }

 .detail-table .el-table__header-wrapper {
   background-color: #f8f9fa;
 }

 .detail-table .el-table th {
   background-color: #f8f9fa !important;
   color: #495057;
   font-weight: 600;
   font-size: 12px;
   padding: 6px;
   border-bottom: 2px solid #e9ecef;
 }

 .detail-table .el-table td {
   font-size: 12px;
   color: #495057;
   padding: 6px;
   border-bottom: 1px solid #f1f3f4;
 }

 .detail-table .el-table--mini td {
   padding: 6px;
 }

 .detail-table .el-table--striped .el-table__body tr.el-table__row--striped td {
   background-color: #fafbfc;
 }

 .detail-table .el-table__body tr:hover td {
   background-color: #f0f7ff !important;
 }

 /* 表格内容样式 */
 .ip-text {
   font-family: 'Courier New', monospace;
   color: #007bff;
   font-weight: 500;
 }

 .time-text {
   color: #6c757d;
   font-size: 11px;
 }

 .location-text {
   color: #495057;
   line-height: 1.4;
 }

 /* 滚动条样式 */
 .detail-table .el-table__body-wrapper {
   scrollbar-width: thin;
   scrollbar-color: #c1c1c1 #f1f1f1;
 }

 .detail-table .el-table__body-wrapper::-webkit-scrollbar {
   width: 6px;
   height: 6px;
 }

 .detail-table .el-table__body-wrapper::-webkit-scrollbar-track {
   background: #f1f1f1;
   border-radius: 3px;
 }

 .detail-table .el-table__body-wrapper::-webkit-scrollbar-thumb {
   background: #c1c1c1;
   border-radius: 3px;
 }

 .detail-table .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
   background: #a8a8a8;
 }

 /* 空数据状态 */
 .detail-table .el-table__empty-text {
   color: #909399;
   font-size: 12px;
   padding: 20px 0;
 }
.tracking-detail-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.des-text {
    color: #909399;
    display: inline;
    font-size: 14px;
}

.des-subject {
    font-size: larger;
    font-weight: 600;
}

.tracking-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
}

.tracking-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    flex-shrink: 0;
    width: 200px;
}

.search-container {
    flex: 1;
    display: flex;
    justify-content: center;
}

.search-input {
    width: 300px;
}

.placeholder {
    width: 200px;
    flex-shrink: 0;
}

.tracking-table-wrapper {
    flex: 1;
    border-radius: 6px;
    border: 1px solid #ebeef5;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.pagination-wrapper {
    padding: 15px 0;
    border-top: 1px solid #ebeef5;
    background-color: #fff;
    flex-shrink: 0;
}

.pagination-wrapper .p-bar {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Element UI 表格样式优化 */
.tracking-table {
    flex: 1;
}

.tracking-table ::v-deep .el-table__header {
    background-color: #f5f7fa;
}

.tracking-table ::v-deep .el-table th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
}

.tracking-table ::v-deep .el-table__row:hover {
    background-color: #f5f7fa;
}

.tracking-table ::v-deep .el-table__body-wrapper {
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.tracking-table ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
}

.tracking-table ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.tracking-table ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.tracking-table ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 收件人信息样式 */

.recipient-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.recipient-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #409eff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    flex-shrink: 0;
}

.recipient-details {
    flex: 1;
    min-width: 0;
}

.recipient-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
}

.recipient-email {
    font-size: 12px;
    color: #909399;
    word-break: break-all;
}

.subject-text {
    font-size: 14px;
    color: #303133;
    word-break: break-word;
    line-height: 1.4;
}

.time-text {
    font-size: 13px;
    color: #606266;
    white-space: nowrap;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.status-sent {
    background-color: #e1f3ff;
    color: #409eff;
}

.status-delivered {
    background-color: #f0f9ff;
    color: #67c23a;
}

.status-read {
    background-color: #fdf6ec;
    color: #e6a23c;
}

.status-opened {
    background-color: #f0f9ff;
    color: #67c23a;
}

.status-clicked {
    background-color: #f5f2ff;
    color: #722ed1;
}

.status-unknown {
    background-color: #f5f5f5;
    color: #909399;
}

/* Element UI 表格自定义样式 */
.tracking-table ::v-deep .el-table__empty-text {
    color: #909399;
}

.tracking-table ::v-deep .el-button--primary-text {
    color: #409eff;
    padding: 0;
}

.tracking-table ::v-deep .el-button--primary-text:hover {
    color: #66b1ff;
}

/* Element UI 按钮样式已内置，无需额外定义 */

/* 响应式设计 */
@media (max-width: 768px) {
    .tracking-detail-container {
        padding: 15px;
        height: 100vh;
    }

    .recipient-info {
        gap: 8px;
    }

    .recipient-avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .tracking-table ::v-deep .el-table__cell {
        padding: 8px 12px;
    }

    .pagination-wrapper {
        padding: 10px 0;
    }
}

@media (max-width: 1200px) {
    .tracking-detail-container {
        height: calc(100vh - 60px);
    }
}

@media (min-width: 1201px) {
    .tracking-detail-container {
        height: calc(100vh - 80px);
    }
}
</style>
