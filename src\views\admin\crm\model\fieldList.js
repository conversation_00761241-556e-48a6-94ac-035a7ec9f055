const fileTypeList = [{
  componentName: 'SingleLineText',
  formType: 'text',
  name: '单行文本',
  icon: require('./img/field_line.png')
},
{
  componentName: 'MultiLineText',
  formType: 'textarea',
  name: '多行文本',
  icon: require('./img/field_multi_line.png')
},
{
  componentName: 'SelectForm',
  formType: 'select',
  name: '下拉框',
  icon: require('./img/field_select.png')
},
{
  componentName: 'CheckboxForm',
  formType: 'checkbox',
  name: '多选',
  icon: require('./img/field_checkbox.png')
},
{
  componentName: 'SingleLineText',
  formType: 'number',
  name: '数字',
  icon: require('./img/field_number.png')
},
{
  componentName: 'SingleLineText',
  formType: 'floatnumber',
  name: '货币',
  icon: require('./img/field_floatnumber.png')
},
{
  componentName: 'SingleLineText',
  formType: 'mobile',
  name: '手机',
  icon: require('./img/field_mobile.png')
},
{
  componentName: 'SingleLineText',
  formType: 'email',
  name: '邮箱',
  icon: require('./img/field_email.png')
},
{
  componentName: 'SingleLineText',
  formType: 'date',
  name: '日期',
  icon: require('./img/field_date.png')
},
{
  componentName: 'SingleLineText',
  formType: 'datetime',
  name: '日期时间',
  icon: require('./img/field_datetime.png')
},
{
  componentName: 'SingleLineText',
  formType: 'user',
  name: '人员',
  icon: require('./img/field_user.png')
},
{
  componentName: 'SingleLineText',
  formType: 'structure',
  name: '部门',
  icon: require('./img/field_structure.png')
},
{
  componentName: 'FileForm',
  formType: 'file',
  name: '附件',
  icon: require('./img/field_file.png')
}
]

export const picField = {
  componentName: 'FileForm',
  formType: 'pic',
  name: '图片',
  icon: require('./img/field_file.png')
}

export default fileTypeList
