<template>
  <div class="sidebar">
    <!-- 邮件/客户邮件切换选项卡 -->
    <div class="email-tabs">
      <div class="tab" @click="switchToEmailTab">邮件</div>
      <div class="tab active">客户邮件</div>
    </div>

    <!-- 客户/客户联系人选项卡 -->
    <div class="customer-tabs">
      <div class="customer-tab" :class="{ active: activeTab === 'customer' }" @click="switchToCustomerTab">
        客户
      </div>
      <div class="customer-tab" :class="{ active: activeTab === 'contact' }" @click="switchToContactTab">
        客户联系人
      </div>
    </div>

    <!-- 客户标签页内容 -->
    <div v-if="activeTab === 'customer'" class="sidebar-section">

      <!-- 客户搜索框 -->
      <div class="contact-search">
        <input type="text" v-model="customerSearchTerm" placeholder="根据关键词搜索客户" class="contact-search-input" />
        <search-icon class="icon-small search-icon" style="left:16px;" />
      </div>

      <!-- 客户列表 -->
      <div class="section-content">
        <div v-for="(customer, index) in filteredCustomers" :key="index" class="sidebar-item"
          :class="{ 'selected': selectedCustomer && selectedCustomer.customerId === customer.customerId }">
          <label class="checkbox-container" @click.stop>
            <input type="checkbox" v-model="customer.selected" @change="updateSelectedCustomerState" />
            <span class="checkmark"></span>
          </label>
          <div class="contact-info" @click="selectCustomer(customer)">
            <users-icon class="icon-small" style="margin-right: 8px; flex-shrink: 0;" />
            <span class="customer-name">{{ customer.customerName }}</span>
          </div>
        </div>
        <div v-if="filteredCustomers.length === 0" class="no-data-container">
          <img class="no-data" src="@/assets/img/noMail.png">
          <div class="no-data-name">暂无数据</div>
        </div>
      </div>

      <!-- 写邮件按钮 -->
      <div class="write-email-container" v-if="emailFlag.writeCustomerEmail && hasSelectedCustomers">
        <button class="write-email-btn" @click="composeEmailToCustomers" v-debounce="[composeEmail, 500]">
          <span class="btn-text">
            <mail-icon class="btn-icon" size=18 />
            写邮件
          </span>
        </button>
      </div>
    </div>

    <!-- 客户联系人标签页内容 -->
    <div v-if="activeTab === 'contact'" class="sidebar-section">

      <!-- 联系人搜索框 -->
      <div class="contact-search">
        <input type="text" v-model="contactSearchTerm" placeholder="搜索联系人" class="contact-search-input" />
        <search-icon class="icon-small search-icon" style="left:16px;" />
      </div>

      <!-- 联系人列表 -->
      <div class="section-content">
        <div v-if="filteredContacts.length === 0" class="no-data-container">
          <img class="no-data" src="@/assets/img/noMail.png">
          <div class="no-data-name">暂无数据</div>
        </div>
        <template v-else>
          <div v-for="(contact, index) in filteredContacts" :key="index" class="sidebar-item"
            :class="{ 'selected': selectedContact && selectedContact.contactsId === contact.contactsId }">
            <label class="checkbox-container" @click.stop>
              <input type="checkbox" v-model="contact.selected" @change="updateSelectedState" />
              <span class="checkmark"></span>
            </label>
            <div class="contact-info" @click="selectContact(contact)">
              <users-icon class="icon-small" style="margin-right: 8px; flex-shrink: 0;" />
              <span class="contact-name">{{ contact.name }}</span>
            </div>
          </div>
        </template>
      </div>

      <!-- 写邮件按钮 -->
      <div class="write-email-container" v-if="emailFlag.writeCustomerEmail && hasSelectedContacts">
        <button class="write-email-btn" @click="composeEmail" v-debounce="[composeEmail, 500]">
          <span class="btn-text">
            <mail-icon class="btn-icon" size=18 />
            写邮件
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ChevronDown, Search, Users, Mail, Check, Square } from 'lucide-vue'
import { crmCustomerIndexAPI } from '@/api/crm/customer.js'
import { crmContactsIndexAPI } from '@/api/crm/contacts.js'
export default {
  name: 'CustomerEmail',
  components: {
    ChevronDownIcon: ChevronDown,
    SearchIcon: Search,
    UsersIcon: Users,
    MailIcon: Mail,
    CheckIcon: Check,
    SquareIcon: Square
  },
  props: {
    emailFlag: {
      type: Object,
      default: () => ({})
    },
    listLoading:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: 'customer', // 默认显示客户标签页
      selectedCustomerFilter: '所有', // 默认客户筛选选项
      selectedContactFilter: '所有', // 默认联系人筛选选项
      showCustomerFilterDropdown: false,
      showContactFilterDropdown: false,
      contactSearchTerm: '',
      customerSearchTerm: '', // 客户搜索关键词

      // 客户筛选选项
      customerFilters: [
        '最近联系',
        '按区域国家',
        '按客户等级',
        '按客户来源',
        '按客户状态',
        '按客户分组',
        '所有',
        '我的',
        '我关注的',
        '成交客户',
        '本周新建客户',
        '共享给我的客户',
        '14天内将掉入公海',
        '从未联系',
        '近7天获取客户',
        '近60天有采购（贸易数据）',
        '未来3个月内采购高峰（贸易数据）'
      ],

      // 联系人筛选选项
      contactFilters: [
        '所有',
        '我的',
        '我关注的'
      ],

      // 客户数据
      customers: [],
      // 联系人数据
      contacts: [],
      // 当前选中的客户
      selectedCustomer: null,
      // 当前选中的联系人
      selectedContact: null,
      // 是否已初始化
      isInitialized: false
    }
  },
  created() {
    this.getCustomerList();
    this.getContactList();
  },
  mounted() {
    // 组件挂载后，如果数据已经加载完成且没有初始化，则进行初始化
    this.$nextTick(() => {
      this.initializeDefaultSelection();
    });
  },
  destroyed(){
    this.customers = [];
    this.contacts = [];
    this.selectedCustomer = null;
    this.selectedContact = null;
  },
  computed: {
    // 根据筛选条件和搜索词过滤客户
    filteredCustomers() {
      // 实际应用中，这里应该根据selectedCustomerFilter和customerSearchTerm进行筛选
      return this.customers.filter(customer => {
        if (!this.customerSearchTerm) return true;
        return customer.customerName.toLowerCase().includes(this.customerSearchTerm.toLowerCase());
      });
    },

    // 根据筛选条件和搜索词过滤联系人
    filteredContacts() {
      // 实际应用中，这里应该根据selectedContactFilter和contactSearchTerm进行筛选
      return this.contacts.filter(contact => {
        if (!this.contactSearchTerm) return true;
        return contact.name.toLowerCase().includes(this.contactSearchTerm.toLowerCase()) ||
          contact.customerName.toLowerCase().includes(this.contactSearchTerm.toLowerCase());
      });
    },

    // 是否有联系人被选中
    hasSelectedContacts() {
      return this.filteredContacts.some(contact => contact.selected);
    },

    // 获取选中的联系人数量
    selectedContactsCount() {
      return this.filteredContacts.filter(contact => contact.selected).length;
    },

    // 是否有客户被选中
    hasSelectedCustomers() {
      return this.filteredCustomers.some(customer => customer.selected);
    }

  },
  methods: {
    // 切换到邮件标签页
    switchToEmailTab() {
      this.$emit('switch-tab', 'email');
    },

    // 切换到客户标签页
    switchToCustomerTab() {
      this.activeTab = 'customer';
      this.selectedContact = null;
      // 如果有客户数据且没有选中的客户，默认选中第一个
      if (this.customers.length > 0 && !this.selectedCustomer) {
        this.selectCustomer(this.customers[0]);
      }
    },

    // 切换到联系人标签页
    switchToContactTab() {
      this.activeTab = 'contact';
      this.selectedCustomer = null;
      // 如果有联系人数据且没有选中的联系人，默认选中第一个
      if (this.contacts.length > 0 && !this.selectedContact) {
        this.selectContact(this.contacts[0]);
      }
    },

    // 初始化默认选择
    initializeDefaultSelection() {
      if (!this.isInitialized) {
        if (this.activeTab === 'customer' && this.customers.length > 0) {
          this.selectCustomer(this.customers[0]);
          this.isInitialized = true;
        } else if (this.activeTab === 'contact' && this.contacts.length > 0) {
          this.selectContact(this.contacts[0]);
          this.isInitialized = true;
        }
      }
    },
    getCustomerList() {
      let params = {
        limit: 20,
        page: 1,
        type: 2,
        search: this.customerSearchTerm
      };
      this.$emit('update-loading', true)
      crmCustomerIndexAPI(params)
        .then(res => {
          this.$emit('update-loading', false)
          const { data } = res;
          this.customers = data.list || [];

          // 如果当前是客户标签页且有数据，默认选中第一个客户
          if (this.activeTab === 'customer' && this.customers.length > 0 && !this.isInitialized) {
            this.selectCustomer(this.customers[0]);
            this.isInitialized = true;
          }
        })
        .catch(() => {
          this.$emit('update-loading', false)
          this.customers = [];
        })
    },
    getContactList() {
      let params = {
        limit: 20,
        page: 1,
        type: 3,
        search: this.contactSearchTerm
      };
      this.$emit('update-loading', true)
      crmContactsIndexAPI(params)
        .then(res => {
          this.$emit('update-loading', false)
          const { data } = res;
          this.contacts = data.list || [];

          // 如果当前是联系人标签页且有数据，默认选中第一个联系人
          if (this.activeTab === 'contact' && this.contacts.length > 0 && !this.isInitialized) {
            this.selectContact(this.contacts[0]);
            this.isInitialized = true;
          }
        })
        .catch(() => {
          this.$emit('update-loading', false)
          this.contacts = [];
        })
    },
    // 切换客户筛选下拉菜单
    toggleCustomerFilterDropdown() {
      this.showCustomerFilterDropdown = !this.showCustomerFilterDropdown;
      if (this.showCustomerFilterDropdown) {
        this.showContactFilterDropdown = false;
      }
    },

    // 切换联系人筛选下拉菜单
    toggleContactFilterDropdown() {
      this.showContactFilterDropdown = !this.showContactFilterDropdown;
      if (this.showContactFilterDropdown) {
        this.showCustomerFilterDropdown = false;
      }
    },

    // 选择客户筛选选项
    selectCustomerFilter(filter) {
      this.selectedCustomerFilter = filter;
      this.showCustomerFilterDropdown = false;
    },

    // 选择联系人筛选选项
    selectContactFilter(filter) {
      this.selectedContactFilter = filter;
      this.showContactFilterDropdown = false;
    },

    // 选择客户
    selectCustomer(customer) {
      console.log('Selected customer:', customer);
      this.selectedCustomer = customer;
      this.selectedContact = null; // 清除联系人选中状态
      // 发出事件通知父组件客户被选中，并加载相关邮件
      this.$emit('customer-selected', customer);
    },

    // 选择联系人
    selectContact(contact) {
      console.log('Selected contact:', contact);
      this.selectedContact = contact;
      this.selectedCustomer = null; // 清除客户选中状态
      // 发出事件通知父组件联系人被选中，并加载相关邮件
      this.$emit('contact-selected', contact);
    },

    // 更新选中状态
    updateSelectedState() {
      // 这个方法在单个联系人的选中状态改变时调用
      // 可以在这里添加额外的逻辑，比如统计选中数量等
    },

    // 更新客户选中状态
    updateSelectedCustomerState() {
      // 这个方法在单个客户的选中状态改变时调用
      // 可以在这里添加额外的逻辑，比如统计选中数量等
    },

    // 写邮件给联系人
    composeEmail() {
      const selectedContacts = this.filteredContacts.filter(contact => contact.selected);
      const emails = selectedContacts.reduce((acc, customer) => {
        if (customer.email) acc.push(customer.email);
        return acc;
      }, []);

      if (emails.length == 0) {
        this.$message.warning('当前选中联系人没有邮箱信息');
      } else {
        // 发出事件通知父组件打开邮件编辑器并填入收件人
        this.$emit('compose-email', {
          to: emails,
          contacts: selectedContacts
        });
      }
    },

    // 写邮件给客户
    composeEmailToCustomers() {
      const selectedCustomers = this.filteredCustomers.filter(customer => customer.selected);
      // 由于客户数据中没有email字段，这里我们假设使用客户名称作为邮箱前缀
      const emails = selectedCustomers.reduce((acc, customer) => {
        if (customer.email) acc.push(customer.email);
        return acc;
      }, []);

      console.log("要发邮件的客户", emails);
      if (emails.length == 0) {
        this.$message.warning('当前选中客户没有邮箱信息');
        return;
      } else {
        // 发出事件通知父组件打开邮件编辑器并填入收件人
        this.$emit('compose-email', {
          to: emails,
          customers: selectedCustomers
        });
      }
    }
  }
}
</script>

<style scoped>
/* 侧边栏样式 */
.sidebar {
  width: 240px;
  height: 100%;
  border-right: 1px solid #e6e9ed;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 顶部导航栏样式 */
.email-tabs {
  display: flex;
  border-bottom: 1px solid #e6e9ed;
  background-color: #f5f7fa;
}

.tab {
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  flex: 1;
  text-align: center;
}

.tab.active {
  color: #0052CC;
  border-bottom: 2px solid #0052CC;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #0052CC;
}

/* 客户/联系人选项卡样式 */
.customer-tabs {
  display: flex;
  border-bottom: 1px solid #e6e9ed;
}

.customer-tab {
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  text-align: center;
  width: 40%;
}

.customer-tab.active {
  color: #0052CC;
}

.customer-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #0052CC;
}

/* 侧边栏部分样式 */
.sidebar-section {
  margin-bottom: 8px;
}

.section-header {
  padding: 12px 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #e6e9ed;
}

.section-content {
  max-height: calc(100vh - 250px);
  /* 增加底部空间，为写邮件按钮留出位置 */
  overflow-y: auto;
  padding-bottom: 50px;
  /* 添加底部内边距，确保内容不被按钮遮挡 */
}

.sidebar-item {
  padding: 6px 16px;
  /* 调整内边距，使内容更紧凑 */
  cursor: pointer;
  display: flex;
  align-items: center;
  /* 确保内容垂直居中 */
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  min-height: 36px;
  /* 设置最小高度，确保足够的空间 */
}

.sidebar-item:hover {
  background-color: #f5f7fa;
}

.sidebar-item.active {
  background-color: #e6f7ff;
  color: #0052CC;
}

.sidebar-item.selected {
  background-color: #e6f7ff;
  color: #0052CC;
  border-left: 3px solid #0052CC;
}

/* 筛选下拉菜单样式 */
.filter-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  /* width: 100%; */
  justify-content: space-between;
}

.filter-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  max-height: 300px;
  background-color: white;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.filter-dropdown-scroll {
  max-height: 300px;
  overflow-y: auto;
}

.filter-item {
  padding: 8px 16px;
  cursor: pointer;
}

.filter-item:hover {
  background-color: #f5f7fa;
}

/* 联系人搜索框样式 */
.contact-search {
  padding: 12px 16px;
  border-bottom: 1px solid #e6e9ed;
  position: relative;
}

.contact-search-input {
  width: 100%;
  padding: 8px 24px;
  padding-right: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.search-icon {
  position: absolute;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

/* 无数据样式 */
.no-data-container {
  text-align: center;
  padding: 20px 0;
  margin-top: 15%;
}

.no-data {
  width: 80px;
  height: 80px;
}

.no-data-name {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

/* 全选容器样式 */
.select-all-container {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  /* 与列表项内边距一致 */
  border-bottom: 1px solid #f0f0f0;
  background-color: #f9f9f9;
  min-height: 40px;
  /* 与列表项最小高度一致 */
}

.select-all-text {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
  line-height: 24px;
  /* 确保文本垂直居中 */
}

/* 复选框容器样式 */
.checkbox-container {
  display: inline-flex;
  position: relative;
  padding-left: 24px;
  /* 增加左侧内边距，为复选框留出更多空间 */
  cursor: pointer;
  user-select: none;
  margin-right: 8px;
  align-items: center;
  /* 确保内容垂直居中 */
  height: 24px;
  /* 固定高度，确保与文本对齐 */
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 50%;
  /* 垂直居中 */
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transform: translateY(-50%);
  /* 确保完全居中 */
}

.checkbox-container:hover input~.checkmark {
  background-color: #f5f5f5;
}

.checkbox-container input:checked~.checkmark {
  background-color: #0052CC;
  border-color: #0052CC;
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.checkbox-container input:checked~.checkmark:after {
  display: block;
}

.checkbox-container .checkmark:after {
  left: 5px;
  top: 3px;
  /* 微调勾选标记的位置 */
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 联系人信息样式 */
.contact-info {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  flex: 1;
  height: 24px;
  /* 与复选框容器高度一致 */
  line-height: 24px;
  /* 确保文本垂直居中 */
}

/* 写邮件按钮容器样式 */
.write-email-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 320px;
  /* 与侧边栏宽度一致 */
  background-color: #fff;
  border-top: 1px solid #e6e9ed;
  z-index: 5;
  padding: 16px;
  /* 与邮件页签的写邮件按钮内边距一致 */
}

.write-email-btn {
  width: 100%;
  padding: 10px 16px;
  /* 与邮件页签的写邮件按钮内边距一致 */
  background-color: #0052CC;
  /* 与邮件页签的写邮件按钮颜色一致 */
  color: white;
  border: none;
  border-radius: 4px;
  /* 与邮件页签的写邮件按钮圆角一致 */
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 与邮件页签的写邮件按钮布局一致 */
  transition: background-color 0.2s ease;
}

.write-email-btn:hover {
  background-color: #0047B3;
  /* 深蓝色悬停效果 */
}

.btn-text {
  display: flex;
  align-items: center;
}

.btn-icon {
  margin-right: 8px;
}

/* 客户名称和联系人名称样式 */
.customer-name,
.contact-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 24px;
  /* 确保文本垂直居中 */
}
</style>
