/**
 * Tab页签管理工具
 * 提供统一的标签页创建和管理功能
 */

import Vue from 'vue'

export const TabManager = {
  /**
   * 创建写邮件标签页
   * @param {Object} options - 邮件选项
   * @param {string} options.to - 收件人
   * @param {string} options.subject - 主题
   * @param {string} options.customerName - 客户名称
   * @param {Object} options.customerData - 客户数据
   * @param {Object} options.contactData - 联系人数据
   */
  createComposeEmailTab(options = {}) {
    // 直接触发事件，让CrmLayout处理路由跳转和Tab创建
    Vue.prototype.$bus.emit('compose-email-from-customer', {
      to: options.to || '',
      subject: options.subject || '',
      customerName: options.customerName || '',
      customerData: options.customerData || null,
      contactData: options.contactData || null,
      content: options.content || '',
      type: 'compose'
    });
  },

  /**
   * 创建模块标签页
   * @param {Object} options - 模块选项
   * @param {string} options.type - 模块类型 (calendar, contacts, customer等)
   * @param {string} options.title - 标签页标题
   * @param {Object} options.data - 模块数据
   */
  createModuleTab(options = {}) {
    Vue.prototype.$bus.emit('open-module-tab', {
      type: options.type,
      title: options.title,
      data: options.data || {}
    });
  },

  /**
   * 创建日历标签页
   * @param {Object} options - 日历选项
   * @param {Date} options.date - 指定日期
   * @param {string} options.view - 视图类型
   */
  createCalendarTab(options = {}) {
    this.createModuleTab({
      type: 'calendar',
      title: options.date ? `日历: ${options.date.toLocaleDateString()}` : '日历',
      data: {
        date: options.date,
        view: options.view || 'month'
      }
    });
  },

  /**
   * 创建联系人标签页
   * @param {Object} options - 联系人选项
   * @param {Object} options.contact - 联系人数据
   * @param {string} options.mode - 模式 (view, edit, new)
   */
  createContactsTab(options = {}) {
    const title = options.contact 
      ? `联系人: ${options.contact.name}` 
      : '新建联系人';
    
    this.createModuleTab({
      type: 'contacts',
      title: title,
      data: {
        contact: options.contact,
        mode: options.mode || 'view'
      }
    });
  },

  /**
   * 创建客户详情标签页
   * @param {Object} options - 客户选项
   * @param {Object} options.customer - 客户数据
   * @param {string} options.mode - 模式 (view, edit)
   */
  createCustomerTab(options = {}) {
    const title = options.customer 
      ? `客户: ${options.customer.name}` 
      : '客户详情';
    
    this.createModuleTab({
      type: 'customer',
      title: title,
      data: {
        customer: options.customer,
        mode: options.mode || 'view'
      }
    });
  },

  /**
   * 从客户页面发送邮件
   * @param {Array|Object} customers - 客户数据（单个或多个）
   */
  composeEmailFromCustomers(customers) {
    const customerList = Array.isArray(customers) ? customers : [customers];
    const emailList = [];
    const customerNames = [];

    customerList.forEach(customer => {
      if (customer.email) {
        emailList.push(customer.email);
        customerNames.push(customer.name || customer.customerName);
      }
    });

    if (emailList.length === 0) {
      Vue.prototype.$message.warning('选中的客户中没有找到邮箱地址');
      return;
    }

    this.createComposeEmailTab({
      to: emailList.join(', '),
      subject: `发给 ${customerNames.join(', ')} 的邮件`,
      customerName: customerNames.join(', '),
      customerData: customerList
    });
  },

  /**
   * 从联系人页面发送邮件
   * @param {Array|Object} contacts - 联系人数据（单个或多个）
   */
  composeEmailFromContacts(contacts) {
    const contactList = Array.isArray(contacts) ? contacts : [contacts];
    const emailList = [];
    const contactNames = [];

    contactList.forEach(contact => {
      if (contact.email) {
        emailList.push(contact.email);
        contactNames.push(contact.name);
      }
    });

    if (emailList.length === 0) {
      Vue.prototype.$message.warning('选中的联系人中没有找到邮箱地址');
      return;
    }

    this.createComposeEmailTab({
      to: emailList.join(', '),
      subject: `发给 ${contactNames.join(', ')} 的邮件`,
      customerName: contactNames.join(', '),
      contactData: contactList
    });
  }
};

// 将TabManager挂载到Vue原型上，方便在组件中使用
Vue.prototype.$tabManager = TabManager;

export default TabManager;
