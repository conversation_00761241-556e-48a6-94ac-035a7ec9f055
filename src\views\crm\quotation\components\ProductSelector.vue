<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="选择产品"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose">
    <div class="product-selector">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入产品名称搜索"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch" />
      </div>

      <!-- 产品列表 -->
      <div class="product-list">
        <el-table
          ref="productTable"
          v-loading="loading"
          :data="productList"
          :height="400"
          @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="55"
            align="center" />
          
          <el-table-column
            prop="productName"
            label="产品名称"
            min-width="150"
            show-overflow-tooltip />
          
          <el-table-column
            prop="categoryName"
            label="产品类型"
            width="120"
            show-overflow-tooltip />
          
          <el-table-column
            prop="unit"
            label="单位"
            width="80" />
          
          <el-table-column
            prop="price"
            label="标准售价"
            width="120">
            <template slot-scope="scope">
              ¥{{ (scope.row.price || 0).toLocaleString() }}
            </template>
          </el-table-column>
          
          <el-table-column
            prop="status"
            label="状态"
            width="80">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.status === 1 ? 'success' : 'info'"
                size="small">
                {{ scope.row.status === 1 ? '上架' : '下架' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        :disabled="selectedProducts.length === 0"
        @click="handleConfirm">确定选择 ({{ selectedProducts.length }})</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { crmProductSaleIndexAPI } from '@/api/crm/product'
import { debounce } from 'throttle-debounce'

export default {
  name: 'ProductSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      searchKeyword: '',
      productList: [],
      selectedProducts: [],
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getProductList()
      } else {
        this.resetData()
      }
    }
  },
  created() {
    // 防抖搜索
    this.handleSearch = debounce(500, this.searchProducts)
  },
  methods: {
    /**
     * 获取产品列表
     */
    getProductList() {
      this.loading = true
      
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        search: this.searchKeyword,
        status: 1 // 只显示上架产品
      }
      
      crmProductSaleIndexAPI(params)
        .then(res => {
          this.productList = res.data.list || []
          this.total = res.data.totalRow || 0
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 搜索产品
     */
    searchProducts() {
      this.currentPage = 1
      this.getProductList()
    },

    /**
     * 选择变更
     */
    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },

    /**
     * 分页大小变更
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.getProductList()
    },

    /**
     * 当前页变更
     */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getProductList()
    },

    /**
     * 确认选择
     */
    handleConfirm() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请选择产品')
        return
      }
      
      this.$emit('confirm', this.selectedProducts)
      this.handleClose()
    },

    /**
     * 关闭弹窗
     */
    handleClose() {
      this.dialogVisible = false
    },

    /**
     * 重置数据
     */
    resetData() {
      this.searchKeyword = ''
      this.productList = []
      this.selectedProducts = []
      this.currentPage = 1
      this.pageSize = 20
      this.total = 0
      
      // 清空表格选择
      this.$nextTick(() => {
        if (this.$refs.productTable) {
          this.$refs.productTable.clearSelection()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.product-selector {
  .search-bar {
    margin-bottom: 16px;
  }
  
  .product-list {
    margin-bottom: 16px;
  }
  
  .pagination-container {
    text-align: center;
  }
}
</style>
