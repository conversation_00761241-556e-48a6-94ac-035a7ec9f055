import request from '@/utils/request'

/**
 * 新建售后维修
 * @param {*} data
 */
export function crmSaleRepairSaveAPI(data) {
  const url = data.entity && data.entity.afterSaleId ? 'update' : 'add'
  return request({
    url: 'crmAfterSale/' + url,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 列表
 * @param {*} data
 */
export function crmSaleRepairIndexAPI(data) {
  return request({
    url: 'crmAfterSale/queryPageList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 详情
 * @param {*} data
 */
export function crmSaleRepairReadAPI(data) {
  return request({
    url: `crmAfterSale/queryById/${data.afterSaleId}`,
    method: 'post'
  })
}

/**
 * 删除
 * @param {*} data
 */
export function crmSaleRepairDeleteAPI(data) {
  return request({
    url: 'crmAfterSale/deleteByIds',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取模具编号历史记录
 * @param {*} data
 */
export function crmSaleRepairMouldNumberHistoryAPI(data) {
  return request({
    url: 'crmAfterSale/queryMouldNumberHistory',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取模具名称列表
 * @param {*} data
 */
export function crmSaleRepairMouldNameListAPI(data) {
  return request({
    url: 'crmAfterSale/queryMouldNameList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}