<template>
  <slide-view
    v-empty="!canShowDetail"
    :listener-ids="listenerIDs"
    :no-listener-ids="noListenerIDs"
    :no-listener-class="noListenerClass"
    :body-style="{padding: 0, height: '100%'}"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限"
    @afterEnter="viewAfterEnter"
    @close="hideView">
    <div
      ref="crmDetailMain"
      v-loading="loading"
      class="detail-main no-padding">
      <flexbox
        v-if="canShowDetail && detailData"
        direction="column"
        align="stretch"
        class="d-container">
        <c-r-m-detail-head
          :id="id"
          :class="{'is-shadow': bodyIsScroll}"
          :detail="detailData"
          :handles="activityHandle"
          :model-data="modelData"
          :crm-type="crmType"
          :page-list="pageList"
          :tag-info="tagInfo"
          @pageChange="pageChange"
          @handle="detailHeadHandle"
          @close="hideView">
          <el-button
            slot="appreciation-call"
            class="wk-premium-info-btn"
            data-type="Call">
            <i class="wk wk-icon-lightning-solid wk-premium-info-icon" data-type="Call" />
            <span class="wk-premium-info-label" data-type="Call">呼叫中心</span>
          </el-button>
          <template slot="name">
            <el-tooltip :content="detailData.star == 0 ? '添加关注' : '取消关注'" effect="dark" placement="top">
              <i
                v-if="detailData.star == 0"
                class="el-icon-star-off focus-icon"
                @click="toggleStar()" />
              <i
                v-else
                class="wk wk-focus-on focus-icon active"
                @click="toggleStar()" />
            </el-tooltip>
          </template>
        </c-r-m-detail-head>

        <div class="d-container-body" @scroll="bodyScroll">
          <detail-head-base :list="headDetails" />
          <relative-stage-records
            v-if="statusShow"
            :id="id"
            :crm-type="crmType"
            :detail="detailData"
            @handle="detailHeadHandle"
          />
          <el-tabs
            v-model="tabCurrentName"
            nav-mode="more"
            class="top-padding">
            <el-tab-pane
              v-for="(item, index) in tabNames"
              :key="index"
              :label="item.label"
              :name="item.name"
              lazy>
              <template slot="label">
                <el-badge
                  :value="item.num"
                  :hidden="item.num <= 0"
                  type="undefined">
                  {{ item.label }}
                </el-badge>
              </template>
              <component
                :is="item.name"
                :id="id"
                :ref="item.name"
                :detail="detailData"
                :type-list="logTyps"
                :handle="activityHandle"
                :crm-type="crmType"
                @handle="detailHeadHandle" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </flexbox>
    </div>
    <c-r-m-all-create
      v-if="isCreate"
      :action="{type: 'update', id: id, batchId: detailData.batchId}"
      :crm-type="crmType"
      @save-success="editSaveSuccess"
      @close="isCreate=false" />
  </slide-view>
</template>

<script>
import { crmContactsReadAPI } from '@/api/crm/contacts'

import SlideView from '@/components/SlideView'
import CRMDetailHead from '../components/CRMDetailHead'
import Activity from '../components/Activity' // 活动
import ContactsImport from './components/ContactsImport' // 重要信息
import CRMEditBaseInfo from '../components/CRMEditBaseInfo' // 联系人基本信息
import RelativeStageRecords from '../components/RelativeStageRecords' // 阶段记录
import RelativeBusiness from '../components/RelativeBusiness' // 相关商机
import RelativeHandle from '../components/RelativeHandle' // 相关操作
import RelativeFiles from '../components/RelativeFiles' // 相关附件
import RelativeTeam from '../components/RelativeTeam' // 团队成员

import CRMAllCreate from '../components/CRMAllCreate' // 新建页面
import DetailMixin from '../mixins/Detail'

export default {
  /** 联系人管理 的 联系人详情 */
  name: 'ContactsDetail',
  components: {
    SlideView,
    CRMDetailHead,
    RelativeStageRecords,
    Activity,
    CRMEditBaseInfo,
    RelativeBusiness,
    RelativeHandle,
    RelativeFiles,
    RelativeTeam,
    CRMAllCreate,
    ContactsImport
  },
  mixins: [DetailMixin],
  props: {
    // 详情信息id
    id: [String, Number],
    // 监听的dom 进行隐藏详情
    listenerIDs: {
      type: Array,
      default: () => {
        return ['crm-main-container']
      }
    },
    // 不监听
    noListenerIDs: {
      type: Array,
      default: () => {
        return []
      }
    },
    noListenerClass: {
      type: Array,
      default: () => {
        return ['el-table__body']
      }
    },
    /** 呼出信息 */
    modelData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 展示加载loading
      loading: false,
      crmType: 'contacts',
      headDetails: [],
      tabCurrentName: 'Activity',
      // 编辑操作
      isCreate: false
    }
  },
  computed: {
    /**
     * 活动操作
     */
    activityHandle() {
      const temps = [
        {
          type: 'task',
          label: '创建任务',
          auth: 'oa.taskExamine'
        }, {
          type: 'business',
          label: '创建商机',
          auth: 'crm.business.save'
        }, {
          type: 'contract',
          label: '创建合同',
          auth: 'crm.contract.save'
        }, {
          type: 'receivables',
          label: '创建回款',
          auth: 'crm.receivables.save'
        }
      ]

      // if (this.canCreateFollowRecord) {
      //   temps = [{
      //     type: 'log',
      //     label: '写跟进'
      //   }].concat(temps)
      // }

      return temps
    },

    /**
     * tabs
     */
    tabNames() {
      var tempsTabs = []
      tempsTabs.push({ label: '活动', name: 'Activity' })
      if (this.crm.contacts && this.crm.contacts.read) {
        tempsTabs.push({ label: '详细资料', name: 'CRMEditBaseInfo' })
      }

      tempsTabs.push({ label: '团队成员', name: 'RelativeTeam', num: this.tabsNumber.memberCount })

      if (this.crm.business && this.crm.business.index) {
        tempsTabs.push({ label: '商机', name: 'RelativeBusiness', num: this.tabsNumber.businessCount })
      }
      tempsTabs.push({ label: '附件', name: 'RelativeFiles', num: this.tabsNumber.fileCount })
      tempsTabs.push({ label: '操作记录', name: 'RelativeHandle', num: 0 })
      // if (this.statusShow) {
      //   tempsTabs.unshift({ label: '阶段记录', name: 'RelativeStageRecords' })
      // }
      return tempsTabs
    },

    /**
     * 根据记录筛选
     */
    logTyps() {
      let logTypslist = []
      logTypslist = [{
        icon: 'all',
        color: '#2362FB',
        command: '',
        label: '全部活动'
      }, {
        icon: 'contacts',
        color: '#27BA4A',
        command: 3,
        label: '联系人'
      }, {
        icon: 'o-task',
        color: '#D376FF',
        command: 11,
        label: '任务'
      }, {
        icon: 'log',
        color: '#5864FF',
        command: 8,
        label: '日志'
      }, {
        icon: 'approve',
        color: '#9376FF',
        command: 9,
        label: '审批'
      }]
      if (this.$store.state.crm.isCall) {
        logTypslist.push({
          icon: 'phone',
          color: '#9376FF',
          command: 13,
          label: '呼叫记录'
        })
      }
      return logTypslist
    },

    /**
     * 客户ID
     */
    customerId() {
      return this.detailData ? this.detailData.customerId || '' : ''
    }
  },
  mounted() {},
  methods: {
    /**
     * 详情
     */
    getDetial() {
      this.loading = true
      crmContactsReadAPI({
        contactsId: this.id
      })
        .then(res => {
          this.loading = false
          const resData = res.data || {}
          this.detailData = resData
          // 负责人
          this.headDetails = [{ title: '客户名称', value: resData.customerName },
            { title: '职务', value: resData.post },
            { title: '手机', value: resData.mobile },
            { title: '创建时间', value: resData.createTime }]
        })
        .catch(() => {
          this.loading = false
          this.hideView()
        })
    },

    /**
     * 关闭
     */
    hideView() {
      this.$emit('hide-view')
    }

    // /**
    //  * 编辑成功
    //  */
    // editSaveSuccess() {
    //   this.$emit('handle', { type: 'save-success' })
    //   this.getDetial()
    // }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/crmdetail.scss";
</style>
