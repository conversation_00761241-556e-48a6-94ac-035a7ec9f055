<template>
  <div class="email-settings-container">
    <!-- 页面标题 -->
    <xr-header label="邮件设置" />

    <div class="main-body">
      <!-- 布局显示部分 -->
      <div class="section">
        <div class="section-title">布局显示</div>
        <div class="layout-options">
          <!-- 三栏布局选项 -->
          <div class="layout-option" @click="selectLayout('three-column')">
            <div class="layout-preview three-column" :class="{ 'selected': selectedLayout === 'three-column' }">
              <div class="radio-container">
                <el-radio v-model="selectedLayout" label="three-column">三栏</el-radio>
              </div>
              <div class="preview-box">
                <div class="preview-column left">
                  <div class="column-items">
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                  </div>
                </div>
                <div class="preview-column middle">
                  <div class="column-items">
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                  </div>
                </div>
                <div class="preview-column right">
                  <div class="email-content">
                    <div class="email-header">邮件主题</div>
                    <div class="email-body"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 上下列表选项 -->
          <div class="layout-option" @click="selectLayout('vertical-list')">
            <div class="layout-preview vertical-list" :class="{ 'selected': selectedLayout === 'vertical-list' }">
              <div class="radio-container">
                <el-radio v-model="selectedLayout" label="vertical-list">上下列表</el-radio>
              </div>
              <div class="preview-box">
                <div class="preview-row top">
                  <div class="row-items">
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                  </div>
                </div>
                <div class="preview-row bottom">
                  <div class="email-content">
                    <div class="email-header">邮件主题</div>
                    <div class="email-body"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 左右列表选项 -->
          <div class="layout-option" @click="selectLayout('horizontal-list')">
            <div class="layout-preview horizontal-list" :class="{ 'selected': selectedLayout === 'horizontal-list' }">
              <div class="radio-container">
                <el-radio v-model="selectedLayout" label="horizontal-list">左右列表</el-radio>
              </div>
              <div class="preview-box">
                <div class="preview-column left-list">
                  <div class="column-items">
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                    <div class="item"></div>
                  </div>
                </div>
                <div class="preview-column right-list">
                  <div class="email-content">
                    <div class="email-header">邮件主题</div>
                    <div class="email-body"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网页设置部分 -->
      <div class="section">
        <div class="section-title">阅读设置</div>
        <div class="settings-options">
          <!-- 下拉选择项 -->
          <div class="form-row">
            <label class="form-label">邮件主页面客户名称显示</label>
            <el-select
              v-model="settings.customerNameDisplay"
              class="form-select">
              <el-option label="客户名称" value="客户名称" />
              <el-option label="客户简称" value="客户简称" />
            </el-select>
          </div>
          
          <!-- 复选框选项 - 分两列显示 -->
          <div class="checkbox-grid">
            <div class="checkbox-column">
              <!-- 收到新邮件时自动刷新 -->
              <el-checkbox v-model="settings.autoRefresh">
                收到新邮件时自动刷新
              </el-checkbox>

              <!-- 关于产品的邮件统一进入产品文件夹 -->
              <el-checkbox v-model="settings.productMailToFolder">
                关于产品的邮件统一进入产品文件夹
              </el-checkbox>

              <!-- 手动开启自动提醒 -->
              <el-checkbox v-model="settings.manualAutoRemind">
                手动开启自动提醒
              </el-checkbox>

              <!-- 转发邮件自动提醒 -->
              <el-checkbox v-model="settings.forwardAutoRemind">
                转发邮件自动提醒
              </el-checkbox>

              <!-- 邮件中的外部人员不允许查看中文资料 -->
              <el-checkbox v-model="settings.restrictExternalAccess">
                邮件中的外部人员不允许查看中文资料
              </el-checkbox>

              <!-- 文件夹不能跨级添加 -->
              <el-checkbox v-model="settings.noCrossFolderAdd">
                文件夹不能跨级添加
              </el-checkbox>

              <!-- 文件夹必须选择上级文件夹 -->
              <el-checkbox v-model="settings.requireParentFolder">
                文件夹必须选择上级文件夹
              </el-checkbox>

              <!-- 显示邮件所有外链 -->
              <el-checkbox v-model="settings.showAllExternalLinks">
                显示邮件所有外链
              </el-checkbox>
            </div>
            
            <div class="checkbox-column">
              <!-- 引用邮件更改字体大小 -->
              <el-checkbox v-model="settings.quoteFontSize">
                引用邮件更改字体大小
              </el-checkbox>

              <!-- 邮件中显示收件人 -->
              <el-checkbox v-model="settings.showRecipient">
                邮件中显示收件人
              </el-checkbox>

              <!-- 批量上传时有重复的文件不上传 -->
              <el-checkbox v-model="settings.noDuplicateUpload">
                批量上传时有重复的文件不上传
              </el-checkbox>

              <!-- 列表显示邮件ShiftR -->
              <el-checkbox v-model="settings.showShiftR">
                列表显示邮件ShiftR
              </el-checkbox>

              <!-- 回复邮件ShiftR -->
              <el-checkbox v-model="settings.replyShiftR">
                回复邮件ShiftR
              </el-checkbox>

              <!-- 转发邮件ShiftR -->
              <el-checkbox v-model="settings.forwardShiftR">
                转发邮件ShiftR
              </el-checkbox>

              <!-- 软删除邮件ShiftR -->
              <el-checkbox v-model="settings.softDeleteShiftR">
                软删除邮件ShiftR
              </el-checkbox>

              <!-- 转发时显示邮件ShiftW -->
              <el-checkbox v-model="settings.showForwardShiftW">
                转发时显示邮件ShiftW
              </el-checkbox>
            </div>
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="save-button-container">
        <el-button type="primary" @click="handleSave">保存设置</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import XrHeader from '@/components/XrHeader'

export default {
  name: 'EmailSettings',
  components: {
    XrHeader
  },
  data() {
    return {
      selectedLayout: 'three-column', // 默认选择三栏布局
      settings: {
        autoRefresh: false,
        productMailToFolder: false,
        manualAutoRemind: false,
        forwardAutoRemind: false,
        restrictExternalAccess: false,
        noCrossFolderAdd: false,
        requireParentFolder: false,
        showAllExternalLinks: false,
        quoteFontSize: false,
        showRecipient: false,
        noDuplicateUpload: false,
        showShiftR: false,
        replyShiftR: false,
        forwardShiftR: false,
        softDeleteShiftR: false,
        showForwardShiftW: false,
        customerNameDisplay:''
      }
    }
  },
  methods: {
    selectLayout(layout) {
      this.selectedLayout = layout
    },
    handleSave() {
      // 实现保存设置的逻辑
      const saveData = {
        layout: this.selectedLayout,
        ...this.settings
      }
      console.log('保存的设置:', saveData)
      this.$message.success('设置保存成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.email-settings-container {
  height: 100%;
  background-color: #fff;
  padding: 24px 40px;

  .main-body {
    padding: 20px;
  }

  .section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      color: #333;
    }
  }

  .layout-options {
    display: flex;
    gap: 30px;

    .layout-option {
      cursor: pointer;

      .layout-preview {
        border: 2px solid #e4e7ed;
        border-radius: 6px;
        width: 280px;
        background: #fff;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
        }

        &.selected {
          border-color: #f56c6c;
        }

        .radio-container {
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;

          .el-radio {
            font-weight: 500;
            
            &.is-checked .el-radio__inner {
              border-color: #f56c6c;
              background-color: #f56c6c;
            }
          }
        }

        .preview-box {
          height: 140px;
          padding: 12px;
          display: flex;
          background: #fafafa;
        }

        &.three-column .preview-box {
          .preview-column {
            flex: 1;
            background-color: #fff;
            border: 1px solid #e4e7ed;
            margin: 0 3px;
            border-radius: 3px;
            
            &.left, &.middle {
              .column-items {
                padding: 6px;
                
                .item {
                  height: 8px;
                  background-color: #e4e7ed;
                  margin-bottom: 4px;
                  border-radius: 2px;
                  
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
            
            &.right {
              .email-content {
                padding: 6px;
                
                .email-header {
                  font-size: 10px;
                  color: #333;
                  margin-bottom: 6px;
                  font-weight: 500;
                }
                
                .email-body {
                  height: 60px;
                  background-color: #f5f7fa;
                  border-radius: 2px;
                }
              }
            }
          }
        }

        &.vertical-list .preview-box {
          flex-direction: column;

          .preview-row {
            background-color: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 3px;
            margin: 3px 0;
            
            &.top {
              flex: 1;
              
              .row-items {
                padding: 6px;
                display: flex;
                gap: 4px;
                
                .item {
                  flex: 1;
                  height: 20px;
                  background-color: #e4e7ed;
                  border-radius: 2px;
                }
              }
            }
            
            &.bottom {
              flex: 1;
              
              .email-content {
                padding: 6px;
                
                .email-header {
                  font-size: 10px;
                  color: #333;
                  margin-bottom: 6px;
                  font-weight: 500;
                }
                
                .email-body {
                  height: 40px;
                  background-color: #f5f7fa;
                  border-radius: 2px;
                }
              }
            }
          }
        }

        &.horizontal-list .preview-box {
          .preview-column {
            background-color: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 3px;
            margin: 0 3px;
            
            &.left-list {
              flex: 1;
              
              .column-items {
                padding: 6px;
                
                .item {
                  height: 8px;
                  background-color: #e4e7ed;
                  margin-bottom: 4px;
                  border-radius: 2px;
                  
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
            }
            
            &.right-list {
              flex: 2;
              
              .email-content {
                padding: 6px;
                
                .email-header {
                  font-size: 10px;
                  color: #333;
                  margin-bottom: 6px;
                  font-weight: 500;
                }
                
                .email-body {
                  height: 80px;
                  background-color: #f5f7fa;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
    }
  }

  .settings-options {
    .form-row {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      gap: 12px;

      .form-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        min-width: 180px;
        white-space: nowrap;
      }

      .form-select {
        width: 200px;
      }
    }

    .checkbox-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;

      .checkbox-column {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .el-checkbox {
          margin-right: 0;
          white-space: nowrap;
          
          .el-checkbox__label {
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
  }

  .save-button-container {
    margin-top: 30px;
    text-align: left;
  }
}
</style>