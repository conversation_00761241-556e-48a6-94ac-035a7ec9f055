<template>
  <div class="modal-overlay" v-if="visible" @click="closeModal">
    <div class="modal-content tag-management-modal" @click.stop>
      <div class="modal-header">
        <h3>标签管理</h3>
        <button class="close-modal" @click="closeModal">
          <XIcon class="icon-small" />
        </button>
      </div>
      <div class="modal-body">
        <!-- 已选标签区域 -->
        <div class="selected-tags-area">
          <div v-for="(tag, index) in localSelectedTags" :key="`selected-${tag.tagId}`" class="selected-tag-item"
            :class="tag.type === 2 ? tag.colorClass : 'system'">
            <span>{{ tag.tagName }}</span>
            <XIcon class="icon-tiny" @click="removeSelectedTag(tag)" />
          </div>
          <!-- 搜索框 -->
          <div class="tag-search-box">
            <input type="text" placeholder="请输入标签" v-model="searchTerm" />
            <SearchIcon class="icon-tiny search-icon" />
          </div>
        </div>

        <div class="tag-divider"></div>

        <!-- 系统标签区域 -->
        <div class="tag-section">
          <h4 class="tag-section-title">系统标签</h4>
          <div class="tag-list">
            <div v-for="(tag, index) in filteredSystemTags" :key="index" class="tag-item system-tag"
              @click="toggleTagSelection(tag)">
              <span>{{ tag.tagName }}</span>
            </div>
          </div>
        </div>

        <div class="tag-divider"></div>

        <!-- 自定义标签区域 -->
        <div class="tag-section">
          <h4 class="tag-section-title">自定义标签</h4>
          <div class="tag-list">
            <div v-for="(tag, index) in filteredCustomTags" :key="`custom-${index}`" class="tag-item custom-tag"
              :class="tag.colorClass" @click="toggleTagSelection(tag)">
              <span>{{ tag.tagName }}</span>
              <XIcon class="icon-tiny edit-icon" @click.stop="deleteTag(tag,index)" />
            </div>
            <div class="tag-item custom-tag empty">
              <input type="text" placeholder="添加自定义标签" class="new-tag-input" v-model="newTagName"
                @keyup.enter="addNewTag" />
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" @click="closeModal">取消</button>
        <button class="save-btn" @click="saveChanges">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
import { X, Search, Edit } from 'lucide-vue';
import { addEmailTagAPI, deleteEmailTagAPI,setEmailTagRelationAPI } from '@/api/crm/email'
export default {
  name: 'TagManagement',
  components: {
    XIcon: X,
    SearchIcon: Search,
    EditIcon: Edit
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    systemTags: {
      type: Array,
      default: () => []
    },
    customTags: {
      type: Array,
      default: () => []
    },
    selectedTags: {
      type: Array,
      default: () => []
    },
    emailId:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      searchTerm: '',
      newTagName: '',
      localSelectedTags: [],
      isInternalUpdate: false // 标记是否为内部更新
    };
  },
  computed: {
    filteredSystemTags() {
      if (!this.searchTerm) return this.systemTags;
      return this.systemTags.filter(tag =>
        tag.tagName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    },
    filteredCustomTags() {
      if (!this.searchTerm) return this.customTags;
      return this.customTags.filter(tag =>
        tag.tagName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 重置并初始化已选标签列表
        this.resetAndInitSelectedTags();
      }
    },
    selectedTags: {
      handler(newVal, oldVal) {
        // 只有在模态框打开且不是由内部操作触发的变化时才重置
        if (this.visible && newVal && !this.isInternalUpdate) {
          // 检查是否真的有变化，避免不必要的重置
          const hasChanged = JSON.stringify(newVal) !== JSON.stringify(oldVal);
          if (hasChanged) {
            this.resetAndInitSelectedTags();
          }
        }
      },
      deep: true
    }
  },
  methods: {
    closeModal() {
      this.$emit('close');
    },

    // 重置并初始化已选标签列表
    resetAndInitSelectedTags() {
      // 清空当前已选标签
      this.localSelectedTags = [];

      // 使用传入的selectedTags初始化
      if (this.selectedTags && this.selectedTags.length > 0) {
        // 深拷贝以避免引用问题
        this.localSelectedTags = JSON.parse(JSON.stringify(this.selectedTags));

        // 确保不会重复添加标签
        this.localSelectedTags = this.localSelectedTags.filter((tag, index, self) =>
          index === self.findIndex(t => t.tagName === tag.tagName)
        );
      }
    },

    toggleTagSelection(tag) {
      // 检查标签是否已经在已选列表中
      const index = this.localSelectedTags.findIndex(t => t.tagId === tag.id);
      if (index === -1) {
        // 确保标签对象包含所有必要的属性
        const tagToAdd = {
          tagId: tag.id,
          tagName: tag.tagName,
          type: tag.type,
          colorClass: tag.colorClass || (tag.type === 2 ? this.getRandomColorClass() : 'red')
        };

        console.log("准备添加的标签:", tagToAdd);

        // 标记为内部更新，防止watch触发重置
        this.isInternalUpdate = true;

        // 使用Vue的响应式方法添加标签
        this.localSelectedTags.push(tagToAdd);

        console.log("添加后的已选标签:", this.localSelectedTags);

        // 实时更新父组件中的标签选择
        this.$emit('tag-selected', this.localSelectedTags);

        // 重置内部更新标记
        this.$nextTick(() => {
          this.isInternalUpdate = false;
        });
      } else {
        console.log("标签已存在，不重复添加");
      }
    },

    // 从已选标签中移除标签
    removeSelectedTag(tag) {
      const index = this.localSelectedTags.findIndex(t => t.tagName === tag.tagName);
      if (index !== -1) {
        this.localSelectedTags.splice(index, 1);

        // 实时更新父组件中的标签选择
        this.$emit('tag-selected', this.localSelectedTags);
      }
    },
    deleteTag(tag,index) {
      this.$confirm('确定删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.loading = true
            deleteEmailTagAPI(
              tag.tagId || tag.id
            )
              .then(res => {
                this.customTags.splice(index, 1);
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                })
              })
              .catch(() => {
                this.loading = false
              })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
    },
    addNewTag() {
      if (!this.newTagName.trim()) return;

      const newTag = {
        tagId: '',
        tagName: this.newTagName.trim(),
        type: 2,
        colorClass: this.getRandomColorClass()
      };
      addEmailTagAPI(newTag)
        .then(res => {
          this.loading = false
          newTag.tagId = res.data;
          this.customTags.push(newTag);
          this.newTagName = '';
          
          setTimeout(() => { 
            this.$emit('refresh-tag-list')
          }, 300)
        })
        .catch(() => {
          this.loading = false
        })
    },
    getRandomColorClass() {
      const colors = ['green', 'purple', 'brown', 'cyan', 'orange', 'red', 'blue','fuchsia'];
      return colors[Math.floor(Math.random() * colors.length)];
    },
    saveChanges() {
      // 确保所有选中的标签（系统和自定义）都会被保存
      this.$emit('save', {
        selectedTags: this.localSelectedTags,
        customTags: this.customTags
      });

      let params = {
        emailId: this.emailId,
        tagIdList:this.localSelectedTags
      };
      setEmailTagRelationAPI(params)
        .then(res => {
          // 实时更新父组件中的标签选择
          this.$emit('tag-selected', this.localSelectedTags);
          this.closeModal();
        })
        .catch(() => {
          this.loading = false
        })
      
    }
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 450px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.close-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: #909399;
  transition: all 0.2s;
}

.close-modal:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #606266;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
  gap: 12px;
}

.cancel-btn,
.save-btn {
  padding: 9px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
}

.cancel-btn {
  background-color: #f4f4f5;
  color: #606266;
  border: 1px solid #dcdfe6;
}

.cancel-btn:hover {
  background-color: #e9e9eb;
  color: #303133;
}

.save-btn {
  background-color: #1890ff;
  color: white;
  border: 1px solid #1890ff;
}

.save-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 标签管理模态框样式 */
.tag-management-modal {
  width: 600px;
  max-width: 95%;
}

.selected-tags-area {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.selected-tag-item {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 16px;
  padding: 4px 10px;
  font-size: 13px;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}

.selected-tag-item:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* 自定义标签颜色类 */
.selected-tag-item.green {
  background-color: #67c23a;
  color: white;
}

.selected-tag-item.purple {
  background-color: #9c27b0;
  color: white;
}

.selected-tag-item.brown {
  background-color: #795548;
  color: white;
}

.selected-tag-item.cyan {
  background-color: #00bcd4;
  color: white;
}

.selected-tag-item.orange {
  background-color: #ff9800;
  color: white;
}

.selected-tag-item.red {
  background-color: #f44336;
  color: white;
}

.selected-tag-item.blue {
  background-color: #2196f3;
  color: white;
}

.selected-tag-item .icon-tiny {
  margin-left: 6px;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s;
}

.selected-tag-item .icon-tiny:hover {
  opacity: 1;
  transform: scale(1.1);
}

.tag-search-box {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.tag-search-box input {
  width: 100%;
  padding: 6px 30px 6px 30px;
  border: 1px solid #dcdfe6;
  border-radius: 16px;
  font-size: 13px;
}

.tag-search-box .search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #909399;
}

.tag-divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 16px 0;
}

.tag-section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #606266;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  display: flex;
  align-items: center;
  border-radius: 16px;
  padding: 5px 12px;
  font-size: 13px;
  cursor: pointer;
  position: relative;
}

.system-tag {
  background-color: #f0f0f0;
  color: #606266;
  transition: all 0.2s;
}

.system-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  filter: brightness(1.1);
}

/* 系统标签统一使用灰色 */
.system-tag {
  background-color: #f0f0f0;
  color: #606266;
}

/* 已选中的系统标签 */
.selected-tag-item.system {
  background-color: #f0f0f0;
  color: #606266;
}

.custom-tag {
  color: white;
  padding-right: 28px;
  transition: all 0.2s;
}

.custom-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  filter: brightness(1.1);
}

.custom-tag.green {
  background-color: #67c23a;
}

.custom-tag.purple {
  background-color: #9c27b0;
}

.custom-tag.brown {
  background-color: #795548;
}

.custom-tag.cyan {
  background-color: #00bcd4;
}

.custom-tag.orange {
  background-color: #ff9800;
}

.custom-tag.red {
  background-color: #f44336;
}

.custom-tag.blue {
  background-color: #2196f3;
}

.custom-tag .edit-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.custom-tag.empty {
  background-color: transparent;
  border: 1px dashed #dcdfe6;
  padding: 4px 12px;
}

.new-tag-input {
  background: transparent;
  border: none;
  outline: none;
  width: 120px;
  font-size: 13px;
}

.icon-small {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.icon-tiny {
  width: 12px;
  height: 12px;
  margin: 0 2px;
  cursor: pointer;
}
</style>
