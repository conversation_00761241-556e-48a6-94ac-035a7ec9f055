export default [{
  componentName: 'FieldInput',
  formType: 'text',
  name: '单行文本',
  type: 1,
  icon: 'wk wk-icon-text'
},
{
  componentName: 'FieldTextarea',
  formType: 'textarea',
  name: '多行文本',
  type: 2,
  icon: 'wk wk-icon-textarea'
},
{
  componentName: 'FieldInput',
  formType: 'website',
  name: '网址',
  type: 25,
  icon: 'wk wk-icon-website'
},

{
  componentName: 'FieldBoolean',
  formType: 'boolean_value',
  name: '布尔值',
  type: 41,
  icon: 'wk wk-icon-bool'
},
{
  componentName: 'FieldSelect',
  formType: 'select',
  name: '单选',
  type: 3,
  icon: 'wk wk-icon-select'
},
{
  componentName: 'FieldCheckbox',
  formType: 'checkbox',
  name: '多选',
  type: 9,
  icon: 'wk wk-icon-checkbox'
},
{
  componentName: 'FieldInput',
  formType: 'number',
  name: '数字',
  type: 5,
  icon: 'wk wk-icon-int'
},
{
  componentName: 'FieldInput',
  formType: 'floatnumber',
  name: '货币',
  type: 6,
  icon: 'wk wk-icon-coin'
},
{
  componentName: 'FieldPercent',
  formType: 'percent',
  name: '百分数',
  type: 42,
  icon: 'wk wk-percent-line'
},
{
  componentName: 'FieldInput',
  formType: 'mobile',
  name: '手机',
  type: 7,
  icon: 'wk wk-icon-mobile2'
},
{
  componentName: 'FieldInput',
  formType: 'email',
  name: '邮箱',
  type: 14,
  icon: 'wk wk-icon-email2'
},
{
  componentName: 'FieldInput',
  formType: 'date',
  name: '日期',
  type: 4,
  icon: 'wk wk-icon-date2'
},
{
  componentName: 'FieldInput',
  formType: 'datetime',
  name: '日期时间',
  type: 13,
  icon: 'wk wk-icon-datetime2'
},
{
  componentName: 'FieldDateInterval',
  formType: 'date_interval',
  name: '日期区间',
  type: 48,
  icon: 'wk wk-icon-range'
},
{
  componentName: 'FieldPosition',
  formType: 'position',
  name: '地址',
  type: 43,
  icon: 'wk wk-nearby'
},
{
  componentName: 'FieldLocation',
  formType: 'location',
  name: '定位',
  type: 44,
  icon: 'wk wk-icon-nav'
},
{
  componentName: 'FieldInput',
  formType: 'user',
  name: '人员',
  type: 10,
  icon: 'wk wk-s-contacts-line'
},
{
  componentName: 'FieldInput',
  formType: 'structure',
  name: '部门',
  type: 12,
  icon: 'wk wk-icon-s-seas-line'
},
{
  componentName: 'FieldFile',
  formType: 'file',
  name: '附件',
  type: 8,
  icon: 'wk wk-icon-file'
},
{
  componentName: 'FieldWritingSign',
  formType: 'handwriting_sign',
  name: '手写签名',
  type: 46,
  icon: 'wk wk-icon-edit-line'
},
{
  componentName: 'FieldDescText',
  formType: 'desc_text',
  name: '描述文字',
  type: 50,
  icon: 'wk wk-icon-des'
},
{
  componentName: 'FieldDetailTable',
  formType: 'detail_table',
  name: '明细表格',
  type: 45,
  icon: 'wk wk-icon-form'
},
{
  componentName: 'FieldFile',
  formType: 'pic',
  name: '图片',
  type: 29,
  icon: 'wk wk-icon-status'
},
{
  componentName: 'FieldSerialNumber',
  formType: 'serial_number',
  name: '自定义编号',
  type: 63,
  icon: 'wk wk-icon-catalog2'
},
{
  componentName: 'FieldTag',
  formType: 'field_tag',
  name: '自定义标签',
  type: 61,
  icon: 'wk wk-icon-label2'
},
{
  componentName: 'FieldAttention',
  formType: 'field_attention',
  name: '关注度',
  type: 62,
  icon: 'wk wk-focus-on-line'
},
{
  componentName: 'FieldGroup',
  formType: 'field_group',
  name: '分组标题',
  type: 60,
  icon: 'wk wk-icon-title'
}
]
