<template>
  <div class="help-test-container">
    <div class="test-header">
      <h2>帮助文档系统测试</h2>
      <el-button
        type="primary"
        @click="showHelp = true">
        打开帮助文档
      </el-button>
    </div>

    <div class="test-content">
      <el-card class="test-card">
        <div slot="header">
          <span>测试说明</span>
        </div>
        <div class="test-info">
          <p><strong>测试目标：</strong>验证帮助文档系统是否正常工作</p>
          <p><strong>测试内容：</strong></p>
          <ul>
            <li>帮助文档菜单加载</li>
            <li>文档内容显示</li>
            <li>系统功能介绍</li>
            <li>反馈功能</li>
          </ul>
          <p><strong>数据来源：</strong>使用模拟数据，包含CRM、OA、人资等模块的帮助文档</p>
        </div>
      </el-card>

      <el-card class="test-card">
        <div slot="header">
          <span>API测试</span>
        </div>
        <div class="api-test">
          <el-button @click="testDocMenu">测试文档菜单API</el-button>
          <el-button @click="testDocContent">测试文档内容API</el-button>
          <el-button @click="testPlatformMenu">测试功能介绍菜单API</el-button>
          <el-button @click="testPlatformContent">测试功能介绍内容API</el-button>
        </div>
        <div v-if="testResult" class="test-result">
          <h4>测试结果：</h4>
          <pre>{{ testResult }}</pre>
        </div>
      </el-card>
    </div>

    <!-- 帮助文档组件 -->
    <help-side
      v-if="showHelp"
      @close="showHelp = false" />
  </div>
</template>

<script>
import HelpSide from '@/views/layout/components/Help/HelpSide'
import request from '@/views/layout/components/Help/request'

export default {
  name: 'HelpTest',
  components: {
    HelpSide
  },
  data() {
    return {
      showHelp: false,
      testResult: null
    }
  },
  methods: {
    /**
     * 测试文档菜单API
     */
    async testDocMenu() {
      try {
        const response = await request.post('/doc')
        this.testResult = JSON.stringify(response.data, null, 2)
        this.$message.success('文档菜单API测试成功')
      } catch (error) {
        this.testResult = `错误: ${error.message}`
        this.$message.error('文档菜单API测试失败')
      }
    },

    /**
     * 测试文档内容API
     */
    async testDocContent() {
      try {
        const response = await request.post('/docInfo', { menu_id: 1 })
        this.testResult = JSON.stringify(response.data, null, 2)
        this.$message.success('文档内容API测试成功')
      } catch (error) {
        this.testResult = `错误: ${error.message}`
        this.$message.error('文档内容API测试失败')
      }
    },

    /**
     * 测试功能介绍菜单API
     */
    async testPlatformMenu() {
      try {
        const response = await request.post('/platformList')
        this.testResult = JSON.stringify(response.data, null, 2)
        this.$message.success('功能介绍菜单API测试成功')
      } catch (error) {
        this.testResult = `错误: ${error.message}`
        this.$message.error('功能介绍菜单API测试失败')
      }
    },

    /**
     * 测试功能介绍内容API
     */
    async testPlatformContent() {
      try {
        const response = await request.post('/platform', { menu_id: 11 })
        this.testResult = JSON.stringify(response.data, null, 2)
        this.$message.success('功能介绍内容API测试成功')
      } catch (error) {
        this.testResult = `错误: ${error.message}`
        this.$message.error('功能介绍内容API测试失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.help-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #333;
  }
}

.test-content {
  display: grid;
  gap: 20px;
}

.test-card {
  .test-info {
    p {
      margin-bottom: 10px;
      line-height: 1.6;
    }
    
    ul {
      margin: 10px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 5px;
      }
    }
  }
  
  .api-test {
    margin-bottom: 20px;
    
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
  
  .test-result {
    h4 {
      margin-bottom: 10px;
      color: #333;
    }
    
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
</style>
