<template>
  <create-view :body-style="{height: '100%'}">
    <div class="details-box">
      <div
        slot="header"
        class="header">
        <span class="text">公告详情</span>
        <span
          class="el-icon-close rt"
          @click="close" />
      </div>
      <div class="content">
        <div class="title">{{ titleList.title }}</div>
        <div class="time">{{ titleList.createTime }}</div>
        <div class="text">{{ titleList.content }}</div>
      </div>
      <div
        v-if="btnShow"
        class="btn-box">
        <el-button
          v-if="permissionUpdate"
          type="primary"
          @click="onEdit">编辑</el-button>
        <el-button
          v-if="permissionDelete"
          type="danger"
          @click="deleteFun">删除</el-button>
      </div>
    </div>
    <v-edit
      v-if="showEdit"
      :form-data="formData"
      :loading="loading"
      @editSubmit="editSubmit"
      @editClose="editClose" />
  </create-view>
</template>

<script>
import CreateView from '@/components/CreateView'
import VEdit from './edit'
// API
import { noticeDeleteAPI, noticeAddOrUpateAPI } from '@/api/oa/notice'
import { mapGetters } from 'vuex'

export default {
  components: {
    CreateView,
    VEdit
  },
  props: {
    titleList: Object,
    btnShow: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showEdit: false,
      formData: {},
      loading: false
    }
  },
  computed: {
    ...mapGetters(['oa']),
    permissionUpdate() {
      return this.oa && this.oa.announcement && this.oa.announcement.update
    },
    permissionDelete() {
      return this.oa && this.oa.announcement && this.oa.announcement.delete
    }
  },
  methods: {
    onEdit() {
      this.formData = Object.assign({}, this.titleList)
      this.showEdit = true
    },
    close() {
      this.$emit('close')
    },
    deleteFun() {
      this.$confirm('确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          noticeDeleteAPI(this.titleList.announcementId).then(res => {
            this.$emit('deleteFun')
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 编辑 -- 取消
    editClose() {
      this.showEdit = false
    },
    // 编辑 -- 确定
    editSubmit() {
      this.loading = true
      noticeAddOrUpateAPI({
        announcementId: this.formData.announcementId,
        title: this.formData.title,
        content: this.formData.content,
        startTime: this.formData.startTime,
        endTime: this.formData.endTime
      })
        .then(res => {
          this.$emit('editSubmit', this.formData)
          this.editClose()
          this.$message.success('公告编辑成功')
          this.loading = false
        })
        .catch(() => {
          this.loading = false
          this.$message.error('公告编辑失败')
        })
    }
  }
}
</script>

<style scoped lang="scss">
$size16: 16px;

.details-box {
  display: flex;
  flex-direction: column;
  height: 100%;

  .header {
    .text {
      font-size: $size16;
    }

    .el-icon-close {
      margin-right: 0;
      font-size: 20px;
      color: #ccc;
      cursor: pointer;
    }
  }

  .content {
    flex: 1;
    margin-top: 10px;
    overflow: auto;

    .title {
      font-size: $size16;
      text-align: center;
    }

    .time {
      margin-top: 8px;
      font-size: 12px;
      color: $--color-text-secondary;
      text-align: center;
    }

    .text {
      padding: 0 20px;
      margin-top: 20px;
      line-height: 24px;
      color: $--color-text-primary;
      word-wrap: break-word;
      white-space: pre-wrap;
    }
  }

  .btn-box {
    padding-right: 20px;
    text-align: right;
  }
}
</style>
