<template>
  <div class="c-container">
    <flexbox v-if="!$slots.icon && showTitle" class="title">{{ title }}<i v-if="helpObj" class="wk wk-icon-fill-help wk-help-tips" :data-type="helpObj.type" :data-id="helpObj.id" /></flexbox>
    <slot name="icon" />
    <!-- <el-input
      v-if="showSearch"
      :placeholder="placeholder"
      v-model="inputContent"
      class="sc-container"
      @input="inputChange"
      @keyup.enter.native="searchInput">
      <el-button
        slot="append"
        type="primary"
        @click.native="searchInput">搜索</el-button>
    </el-input> -->
    <div class="right-container">
      <el-button
        v-if="canSave"
        type="primary"
        @click="createClick">{{ mainTitle }}</el-button>
      <slot name="ft" />
      <el-button
        v-if="showDupCheck && canIndex"
        class="dup-check-btn"
        @click="dupCheckShow = true">查重</el-button>
      <el-dropdown
        v-if="moreTypes.length > 0"
        trigger="click"
        @command="handleTypeDrop">
        <el-button class="dropdown-btn" icon="el-icon-more" />
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="(item, index) in moreTypes"
            :key="index"
            :icon="item.icon | wkIconPre"
            :command="item.type">{{ item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <c-r-m-all-create
      v-if="isCreate"
      :crm-type="createCRMType"
      :action="createActionInfo"
      @save-success="createSaveSuccess"
      @close="hideView" />
    <!-- 查重 -->
    <duplicate-check
      :crm-type="crmType"
      :visible.sync="dupCheckShow" />
  </div>
</template>

<script type="text/javascript">
import { mapGetters } from 'vuex'
import CRMAllCreate from './CRMAllCreate'
import DuplicateCheck from './DuplicateCheck'
import { getPermissionByKey } from '@/utils'

export default {
  name: 'CRMListHead', // 客户管理下 重要提醒 回款计划提醒
  components: {
    CRMAllCreate,
    DuplicateCheck
  },
  props: {
    showTitle: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '内容'
    },
    mainTitle: {
      type: String,
      default: ''
    },
    // CRM类型
    crmType: {
      type: String,
      default: ''
    },
    /** 是公海 */
    isSeas: {
      type: Boolean,
      default: false
    },
    poolId: [String, Number],
    // 公海权限
    poolAuth: Object,
    // showSearch: {
    //   type: Boolean,
    //   default: true
    // },
    search: String, // 用于联动
    // 自定义方法
    createFun: Function
  },
  data() {
    return {
      inputContent: '',
      // 创建的相关信息
      createActionInfo: { type: 'save' },
      createCRMType: '',
      isCreate: false, // 是创建
      // 查重
      dupCheckShow: false
    }
  },
  computed: {
    ...mapGetters(['crm', 'userInfo', 'manage']),
    canSave() {
      if (this.isSeas) {
        return false
      }

      return this.$auth(`crm.${this.crmType}.save`)
    },

    canIndex() {
      return this.$auth(`crm.${this.crmType}.index`)
    },

    titleIcon() {
      return require(`@/assets/img/crm/${this.crmType}.png`)
    },

    showDupCheck() {
      return ['leads', 'customer', 'contacts'].includes(this.crmType) && !this.isSeas
    },

    // 更多操作
    moreTypes() {
      const moreTypes = []
      let importAuth = this.$auth(`crm.${this.crmType}.excelimport`)
      if (this.isSeas && this.poolId) {
        importAuth = this.poolAuth && this.poolAuth.excelexport
      }
      if (importAuth) {
        moreTypes.push({ type: 'enter', name: '导入', icon: 'import' })
      }

      let exportAuth = this.$auth(`crm.${this.crmType}.excelexport`)
      if (this.isSeas && this.poolId) {
        exportAuth = this.poolAuth && this.poolAuth.excelexport
      }
      if (exportAuth) {
        moreTypes.push({ type: 'out', name: '导出', icon: 'export' })
      }

      if (this.isSeas && getPermissionByKey('manage.crm.pool')) {
        moreTypes.push({ type: 'seasSet', name: '公海规则', icon: 'manage' })
      }
      return moreTypes
    },

    // 帮助信息
    helpObj() {
      return {
        leads: {
          type: '7',
          id: '24'
        }, customer: {
          type: '8',
          id: this.isSeas ? '118' : '25'
        }, contacts: {
          type: '9',
          id: '26'
        }, business: {
          type: '10',
          id: '27'
        }, contract: {
          type: '11',
          id: '28'
        }, receivables: {
          type: '12',
          id: '29'
        }, invoice: {
          type: '13',
          id: '30'
        }, visit: {
          type: '14',
          id: '31'
        }, product: {
          type: '15',
          id: '32'
        }, marketing: {
          type: '16',
          id: '33'
        }
      }[this.crmType] || null
    }
  },
  mounted() {
    // 监听导入
    this.$bus.on('import-crm-done-bus', (type) => {
      if (this.crmType == type) {
        this.$emit('on-handle', { type: 'import-crm' })
      }
    })
  },
  beforeDestroy() {
    this.$bus.off('import-crm-done-bus')
  },
  methods: {
    handleTypeDrop(command) {
      if (command == 'out') {
        this.$emit('on-export')
      } else if (command == 'enter') {
        console.log(this.$wkImport)
        this.$wkImport.import(this.crmType, {
          ownerSelectShow: false, // 去除选择负责人逻辑
          poolSelectShow: this.isSeas,
          userInfo: this.userInfo
        })
      } else if (command == 'seasSet') {
        this.$router.push('/manage/customer/customer')
      }
    },
    createClick() {
      if (this.createFun) {
        this.createFun()
      } else {
        this.createCRMType = this.crmType
        this.createActionInfo = { type: 'save' }
        this.isCreate = !this.isCreate
      }
    },
    // inputChange() {
    //   this.$emit('update:search', this.inputContent)
    // },
    // // 进行搜索操作
    // searchInput() {
    //   this.$emit('on-search', this.inputContent)
    // },
    // 创建数据页面 保存成功
    createSaveSuccess(data) {
      if (data && data.createContacts) {
        if (data.type == 'customer') {
          this.createCRMType = 'contacts'
          this.createActionInfo = {
            type: 'relative',
            crmType: 'customer',
            data: {}
          }
          this.createActionInfo.data['customer'] = data.data
          this.isCreate = true
        }
      } else {
        // 回到保存成功
        this.$emit('on-handle', { type: 'save-success' })
      }
    },
    hideView() {
      this.isCreate = false
    }
  }
}
</script>
<style lang="scss" scoped>
.c-container {
  position: relative;
  height: 56px;
  padding: 24px 40px 0;

  .title {
    float: left;
    width: auto;
    height: 100%;
    font-size: $--font-size-xxlarge;
    font-weight: $--font-weight-primary;
    color: $--color-text-primary;
  }

  .sc-container {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    margin: -16px 0 0 -150px;
  }

  // .el-input {
  //   ::v-deep .el-input-group__append {
  //     background-color: $--color-primary;
  //     border-color: $--color-primary;
  //     color: white;
  //   }
  // }

  .right-container {
    position: relative;
    float: right;
    font-size: 0;
    text-align: right;

    .dropdown-btn {
      padding: 8px;
      margin-left: $--button-button-margin-left;
    }
  }
}
</style>
