<template>
  <div
    v-loading="loading"
    v-empty="nopermission"
    class="rc-cont"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限">
    <!-- <div
      v-if="!isSeas"
      class="rc-head">
      <el-button
        v-if="contractSave"
        icon="el-icon-plus"
        @click="createClick">新建订单</el-button>
    </div> -->
    <el-table
      :data="list"
      :height="tableHeight"
      :cell-class-name="cellClassName"
      stripe>
      <el-table-column
        v-for="(item, index) in fieldList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        show-overflow-tooltip>
        <template slot-scope="{ row, column, $index }">
          <template v-if="item.prop == 'checkStatus'">
            <span :style="getStatusStyle(row.checkStatus)" class="status-mark" />
            <span>{{ getStatusName(row.checkStatus) }}</span>
          </template>
          <template
            v-else>
            {{ fieldFormatter(row, column) }}
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script type="text/javascript">
import { crmCustomerQueryContractAPI } from '@/api/crm/customer'
import { crmBusinessQueryContractAPI } from '@/api/crm/business'
import { separator } from '@/filters/vueNumeralFilter/filters'
import CheckStatusMixin from '@/mixins/CheckStatusMixin'

export default {
  name: 'RelativeOrder', // 相关联系人  可能再很多地方展示 放到客户管理目录下
  mixins: [CheckStatusMixin],
  props: {
    /** 模块ID */
    id: [String, Number],
    /** 没有值就是全部类型 有值就是当个类型 */
    crmType: {
      type: String,
      default: ''
    },
    /** 联系人人下 新建商机 需要联系人里的客户信息  合同下需要客户和商机信息 */
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      nopermission: false,
      list: [],
      fieldList: [],
      tableHeight: '400px',
      contractId: '', // 查看全屏联系人详情的 ID
      // 创建的相关信息
    }
  },
  inject: ['rootTabs'],
  watch: {
    id(val) {
      this.list = []
      this.getDetail()
    },

    'rootTabs.currentName'(val) {
      if (val === 'RelativeOrder') {
        this.getDetail(false)
      }
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    getFieldList() {
      this.fieldList.push({ prop: 'orderNumber', width: '200', label: '订单编号' })
      this.fieldList.push({
        prop: 'customerId',
        width: '200',
        label: '客户ID'
      })
      this.fieldList.push({
        prop: 'customerName',
        width: '200',
        label: '客户名称'
      })
      this.fieldList.push({
        prop: 'startTime',
        width: '200',
        label: '模具类型'
      })
      this.fieldList.push({
        prop: 'startTime',
        width: '200',
        label: '模具编号'
      })
      this.fieldList.push({ prop: 'money', width: '200', label: '模具名称' })
      this.fieldList.push({ prop: 'endTime', width: '200', label: '规格型号' })
      this.fieldList.push({ prop: 'endTime', width: '200', label: '销售数量' })
      this.fieldList.push({ prop: 'endTime', width: '200', label: '销售单位' })
      this.fieldList.push({ prop: 'endTime', width: '200', label: '含税单价' })
      this.fieldList.push({ prop: 'checkStatus', width: '200', label: '订单状态' })
    },

    getDetail(loading = true) {
      this.loading = loading
      const request = {
        customer: crmCustomerQueryContractAPI,
        business: crmBusinessQueryContractAPI
      }[this.crmType]
      const params = { pageType: 0 }
      params[this.crmType + 'Id'] = this.id
      request(params)
        .then(res => {
          if (this.fieldList.length == 0) {
            this.getFieldList()
          }
          this.nopermission = false
          this.loading = false
          this.list = res.data.list
        })
        .catch(data => {
          if (data.code == 102) {
            this.nopermission = true
          }
          this.loading = false
        })
    },

    /**
     * 格式化字段
     */
    fieldFormatter(row, column) {
      // 如果需要格式化
      if (column.property == 'money') {
        return separator(row[column.property] || 0)
      }
      return row[column.property] === '' || row[column.property] === null ? '--' : row[column.property]
    },

    /**
     * 当某一行被点击时会触发该事件
     */

    /**
     * @description: 状态颜色
     * @param {*} status
     * @return {*}
     */
    getStatusStyle(status) {
      return {
        backgroundColor: this.getStatusColor(status)
      }
    },

    /**
     * 通过回调控制class
     */
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'contractName') {
        return 'can-visit--underline'
      } else if (column.property === 'money') {
        return 'is-floatnumber-column'
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../styles/relativecrm.scss";
@import "../styles/table.scss";
</style>
