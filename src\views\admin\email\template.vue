<template>
  <div v-loading="loading" class="email-template-container">
    <!-- 页面头部 -->
    <xr-header label="模板管理" />
    
    <div class="main-body">
      <!-- 顶部搜索区域 -->
      <div class="search-section">
        <div class="search-form">
          <el-form :inline="true" :model="searchForm" class="demo-form-inline">
            <el-form-item>
              <el-select v-model="searchForm.company" placeholder="全公司" clearable @change="handleSearch">
                <el-option label="全公司" value=""></el-option>
                <el-option 
                  v-for="company in companyList" 
                  :key="company.value" 
                  :label="company.label" 
                  :value="company.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleCreateTemplate">新建模板</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        style="width: 100%"
        border
        stripe
      >
        <el-table-column prop="templateName" label="模板名称" width="200">
        </el-table-column>
        <el-table-column prop="usage" label="适用于" width="200">
        </el-table-column>
        <el-table-column prop="creator" label="创建人" width="150">
        </el-table-column>
        <el-table-column prop="operation" label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </div>

    <!-- 模板配置区域 -->
    <div class="template-config-section">
      <div class="config-header">
        <h3>模板设置</h3>
        <el-button type="primary" @click="saveTemplateConfig">保存配置</el-button>
      </div>
      <div class="config-grid">
        <!-- 默认设置 -->
        <div class="config-column">
          <h4>默认设置</h4>
          <div class="config-item">
            <label>公司所有人员认证模板</label>
          </div>
          <div class="config-item">
            <label>本人所有认证模板</label>
          </div>
          <div class="config-item">
            <label><EMAIL></label>
          </div>
        </div>

        <!-- 新建模板 -->
        <div class="config-column">
          <h4>新建模板</h4>
          <div class="config-item">
            <el-select v-model="newTemplate.type" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="type in templateTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value">
              </el-option>
            </el-select>
          </div>
          <div class="config-item">
            <el-select v-model="newTemplate.category" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="category in templateCategories" 
                :key="category.value" 
                :label="category.label" 
                :value="category.value">
              </el-option>
            </el-select>
          </div>
          <div class="config-item">
            <el-select v-model="newTemplate.template" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="template in templateCategories" 
                :key="template.value" 
                :label="template.label" 
                :value="template.value">
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 回复模板 -->
        <div class="config-column">
          <h4>回复模板</h4>
          <div class="config-item">
            <el-select v-model="replyTemplate.type" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option label="自动回复" value="auto"></el-option>
              <el-option label="手动回复" value="manual"></el-option>
            </el-select>
          </div>
          <div class="config-item">
            <el-select v-model="replyTemplate.category" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="category in templateCategories" 
                :key="category.value" 
                :label="category.label" 
                :value="category.value">
              </el-option>
            </el-select>
          </div>
          <div class="config-item">
            <el-select v-model="replyTemplate.template" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="template in templateCategories" 
                :key="template.value" 
                :label="template.label" 
                :value="template.value">
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 转发模板 -->
        <div class="config-column">
          <h4>转发模板</h4>
          <div class="config-item">
            <el-select v-model="forwardTemplate.type" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option label="内部转发" value="internal"></el-option>
              <el-option label="外部转发" value="external"></el-option>
            </el-select>
          </div>
          <div class="config-item">
            <el-select v-model="forwardTemplate.category" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="category in templateCategories" 
                :key="category.value" 
                :label="category.label" 
                :value="category.value">
              </el-option>
            </el-select>
          </div>
          <div class="config-item">
            <el-select v-model="forwardTemplate.template" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
              <el-option 
                v-for="template in templateCategories" 
                :key="template.value" 
                :label="template.label" 
                :value="template.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script>

import XrHeader from '@/components/XrHeader'

export default {
  name: 'EmailTemplate',
  components: {
    XrHeader
  },
  data() {
    return {
      loading: false,
      // 搜索表单
      searchForm: {
        company: ''
      },
      // 公司列表
      companyList: [],
      // 表格数据
      tableData: [],
      // 分页
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 默认配置
      defaultConfig: {
        companyAuth: '',
        personalAuth: '',
        salesEmail: ''
      },
      // 新建模板
      newTemplate: {
        type: '',
        category: '',
        template: ''
      },
      // 回复模板
      replyTemplate: {
        type: '',
        category: '',
        template: ''
      },
      // 转发模板
      forwardTemplate: {
        type: '',
        category: '',
        template: ''
      },
      // 模板分类列表
      templateCategories: [],
      // 模板类型选项
      templateTypes: [
        { label: '认证模板', value: 'auth' },
        { label: '通知模板', value: 'notify' },
        { label: '自动回复', value: 'auto' },
        { label: '手动回复', value: 'manual' },
        { label: '内部转发', value: 'internal' },
        { label: '外部转发', value: 'external' }
      ]
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      await Promise.all([
        // this.loadCompanyList(),
        // this.loadTemplateCategoryList(),
        // this.loadTemplateData(),
        // this.loadTemplateConfig()
      ])
    },

    // 加载公司列表
    async loadCompanyList() {
      try {
        const res = await getCompanyListAPI()
        this.companyList = res.data || []
      } catch (error) {
        console.error('加载公司列表失败:', error)
      }
    },

    // 加载模板分类列表
    async loadTemplateCategoryList() {
      try {
        const res = await getTemplateCategoryListAPI()
        this.templateCategories = res.data || []
      } catch (error) {
        console.error('加载模板分类失败:', error)
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadTemplateData()
    },
    
    // 编辑模板
    handleEdit(row) {
      this.$message.info('编辑模板功能开发中')
      // TODO: 实现编辑功能
      // this.$router.push({
      //   name: 'emailTemplateEdit',
      //   params: { id: row.id }
      // })
    },
    
    // 删除模板
    handleDelete(row) {
      this.$confirm('确认删除该模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteEmailTemplateAPI(row.id)
          this.$message.success('删除成功')
          this.loadTemplateData()
        } catch (error) {
          this.$message.error('删除失败')
          console.error('删除模板失败:', error)
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.loadTemplateData()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadTemplateData()
    },
    
    // 加载模板数据
    async loadTemplateData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize,
          company: this.searchForm.company
        }
        
        const res = await getEmailTemplateListAPI(params)
        this.tableData = res.data.records || []
        this.pagination.total = res.data.totalRow || 0
      } catch (error) {
        this.$message.error('加载模板数据失败')
        console.error('加载模板数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 加载模板配置
    async loadTemplateConfig() {
      try {
        const res = await getTemplateConfigAPI()
        if (res.data) {
          this.defaultConfig = res.data.defaultConfig || {}
          this.newTemplate = res.data.newTemplate || {}
          this.replyTemplate = res.data.replyTemplate || {}
          this.forwardTemplate = res.data.forwardTemplate || {}
        }
      } catch (error) {
        console.error('加载模板配置失败:', error)
      }
    },

    // 保存模板配置
    async saveTemplateConfig() {
      try {
        const configData = {
          defaultConfig: this.defaultConfig,
          newTemplate: this.newTemplate,
          replyTemplate: this.replyTemplate,
          forwardTemplate: this.forwardTemplate
        }
        
        await saveTemplateConfigAPI(configData)
        this.$message.success('配置保存成功')
      } catch (error) {
        this.$message.error('配置保存失败')
        console.error('保存模板配置失败:', error)
      }
    },

    // 新建模板
    handleCreateTemplate() {
      this.$message.info('新建模板功能开发中')
      // TODO: 实现新建功能
      // this.$router.push({ name: 'emailTemplateCreate' })
    }
  }
}
</script>

<style scoped>
.email-template-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.search-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form .el-form-item {
  margin-right: 20px;
}

.table-section {
  background: white;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.template-config-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.config-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 500;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.config-column h4 {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.config-item {
  line-height: 35px;
  margin-bottom: 10px;
}

.config-item label {
  display: block;
  margin-bottom: 5px;
  color: #666;
}

.config-item .el-select {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>