<template>
  <div class="main-container">
    <flexbox class="main-header">
      <div class="main-header-left">
        <span class="main-header-title">帮助文档管理</span>
      </div>
      <div class="main-header-right">
        <el-button
          type="primary"
          @click="handleAdd">
          新增文档
        </el-button>
      </div>
    </flexbox>

    <div class="main-body">
      <el-table
        v-loading="loading"
        :data="list"
        :height="tableHeight"
        stripe
        border
        highlight-current-row
        style="width: 100%">
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          align="center" />
        <el-table-column
          prop="name"
          label="文档名称"
          min-width="200" />
        <el-table-column
          prop="categoryName"
          label="分类"
          width="120" />
        <el-table-column
          prop="type"
          label="类型"
          width="120">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 1 ? 'primary' : 'success'">
              {{ scope.row.type === 1 ? '帮助手册' : '功能介绍' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="160" />
        <el-table-column
          prop="status"
          label="状态"
          width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="200"
          fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handlePreview(scope.row)">
              预览
            </el-button>
            <el-button
              type="text"
              size="small"
              :class="scope.row.status === 1 ? 'is-danger' : 'is-success'"
              @click="handleToggleStatus(scope.row)">
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="text"
              size="small"
              class="is-danger"
              @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="p-contianer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :total="total"
          class="p-bar"
          background
          layout="prev, pager, next, sizes, total, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <help-doc-create
      v-if="showCreate"
      :visible.sync="showCreate"
      :detail="editData"
      @save-success="getList" />

    <!-- 预览弹窗 -->
    <help-doc-preview
      v-if="showPreview"
      :visible.sync="showPreview"
      :detail="previewData" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Flexbox from '@/components/Flexbox'
import HelpDocCreate from './components/HelpDocCreate'
import HelpDocPreview from './components/HelpDocPreview'

export default {
  name: 'HelpDocIndex',
  components: {
    Flexbox,
    HelpDocCreate,
    HelpDocPreview
  },
  data() {
    return {
      loading: false,
      list: [],
      currentPage: 1,
      pageSize: 15,
      pageSizes: [15, 20, 30, 40],
      total: 0,
      showCreate: false,
      showPreview: false,
      editData: null,
      previewData: null
    }
  },
  computed: {
    ...mapGetters(['collapse']),
    tableHeight() {
      return this.$store.state.app.tableHeight
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /**
     * 获取列表数据
     */
    getList() {
      this.loading = true
      // 模拟数据
      setTimeout(() => {
        this.list = [
          {
            id: 1,
            name: '客户管理',
            categoryName: 'CRM系统',
            type: 1,
            status: 1,
            updateTime: '2024-01-15 10:30:00'
          },
          {
            id: 2,
            name: '联系人管理',
            categoryName: 'CRM系统',
            type: 1,
            status: 1,
            updateTime: '2024-01-15 10:30:00'
          },
          {
            id: 3,
            name: '商机管理',
            categoryName: 'CRM系统',
            type: 1,
            status: 1,
            updateTime: '2024-01-15 10:30:00'
          },
          {
            id: 4,
            name: '客户管理功能介绍',
            categoryName: 'CRM系统',
            type: 2,
            status: 1,
            updateTime: '2024-01-15 10:30:00'
          }
        ]
        this.total = this.list.length
        this.loading = false
      }, 500)
    },

    /**
     * 新增
     */
    handleAdd() {
      this.editData = null
      this.showCreate = true
    },

    /**
     * 编辑
     */
    handleEdit(row) {
      this.editData = { ...row }
      this.showCreate = true
    },

    /**
     * 预览
     */
    handlePreview(row) {
      this.previewData = { ...row }
      this.showPreview = true
    },

    /**
     * 切换状态
     */
    handleToggleStatus(row) {
      const action = row.status === 1 ? '禁用' : '启用'
      this.$confirm(`确定要${action}该文档吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用API切换状态
        row.status = row.status === 1 ? 0 : 1
        this.$message.success(`${action}成功`)
      }).catch(() => {})
    },

    /**
     * 删除
     */
    handleDelete(row) {
      this.$confirm('确定要删除该文档吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用API删除
        const index = this.list.findIndex(item => item.id === row.id)
        if (index > -1) {
          this.list.splice(index, 1)
          this.total--
        }
        this.$message.success('删除成功')
      }).catch(() => {})
    },

    /**
     * 分页大小改变
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },

    /**
     * 当前页改变
     */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  background-color: white;
  height: 100%;
}

.main-header {
  padding: 15px 20px 10px;
  .main-header-title {
    font-size: 16px;
    color: #333;
  }
}

.main-body {
  padding: 0 20px 20px;
}

.p-contianer {
  position: relative;
  background-color: white;
  height: 44px;
  .p-bar {
    float: right;
    margin: 5px 100px 0 0;
    font-size: 14px !important;
  }
}
</style>
