<template>
  <div class="send-rules-container">
    <!-- 页面标题 -->
    <xr-header label="邮件收发件规则" />

    <div class="main-body">
      <!-- 规则列表 -->
      <div class="rules-section">
        <div class="section-header">
          <div class="section-title">规则列表</div>
          <el-button type="primary" @click="handleAddRule">新建规则</el-button>
        </div>
        
        <!-- 筛选区域 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="4">
              <el-select v-model="filterForm.ruleType" placeholder="全部" clearable style="width: 100%">
                <el-option label="全部" value=""></el-option>
                <el-option label="收件规则" value="receive"></el-option>
                <el-option label="发件规则" value="send"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filterForm.creator" placeholder="创建人" clearable style="width: 100%">
                <el-option label="全部" value=""></el-option>
                <el-option label="管理员" value="admin"></el-option>
                <el-option label="系统" value="system"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-input v-model="filterForm.keyword" placeholder="规则名称" clearable style="width: 100%">
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="handleFilter">查询</el-button>
            </el-col>
          </el-row>
        </div>

        <div class="rules-table">
          <el-table
            v-loading="loading"
            :data="filteredList"
            style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="60">
            </el-table-column>
            <el-table-column
              prop="ruleName"
              label="规则名称"
              min-width="150">
            </el-table-column>
            <el-table-column
              prop="ruleType"
              label="适用规则"
              width="150">
              <template slot-scope="scope">
                <span>{{ getRuleTypeName(scope.row.ruleType) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="actions"
              label="执行操作"
              min-width="150">
              <template slot-scope="scope">
                <div v-for="(action, index) in scope.row.actions" :key="index" class="action-item">
                  {{ getActionText(action) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="creator"
              label="创建人"
              width="150">
              <template slot-scope="scope">
                {{ scope.row.creator || '管理员' }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              width="250">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleEdit(scope.row)">编辑</el-button>
                <el-button
                  type="text"
                  @click="handleMoveUp(scope.$index)" 
                  :disabled="scope.$index === 0">上移</el-button>
                <el-button
                  type="text"
                  @click="handleMoveDown(scope.$index)" 
                  :disabled="scope.$index === rulesList.length - 1">下移</el-button>
                <el-button
                  type="text"
                  class="delete-btn"
                  @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-if="rulesList.length === 0" class="empty-data">
            <img src="@/assets/img/empty.png" class="empty-img">
            <div class="empty-text">暂无规则数据</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 规则编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="650px" @close="handleDialogClose">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="ruleForm.ruleName" placeholder="请输入规则名称"></el-input>
        </el-form-item>
        <el-form-item label="规则类型" prop="ruleType">
          <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型">
            <el-option label="收件规则" value="receive"></el-option>
            <el-option label="发件规则" value="send"></el-option>
          </el-select>
        </el-form-item>
        
        <!-- 条件设置 -->
        <el-form-item label="条件设置">
          <div class="condition-list">
            <div v-for="(condition, index) in ruleForm.conditions" :key="index" class="condition-item-form">
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-select v-model="condition.field" placeholder="选择字段">
                    <el-option label="发件人" value="sender"></el-option>
                    <el-option label="收件人" value="recipient"></el-option>
                    <el-option label="主题" value="subject"></el-option>
                    <el-option label="内容" value="content"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="7">
                  <el-select v-model="condition.operator" placeholder="选择条件">
                    <el-option label="包含" value="contains"></el-option>
                    <el-option label="不包含" value="notContains"></el-option>
                    <el-option label="等于" value="equals"></el-option>
                    <el-option label="开头是" value="startsWith"></el-option>
                    <el-option label="结尾是" value="endsWith"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="7">
                  <el-input v-model="condition.value" placeholder="输入值"></el-input>
                </el-col>
                <el-col :span="3" class="condition-actions">
                  <el-button 
                    type="text" 
                    icon="el-icon-delete" 
                    @click="removeCondition(index)"
                    :disabled="ruleForm.conditions.length <= 1">
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <div class="add-condition">
              <el-button type="text" icon="el-icon-plus" @click="addCondition">添加条件</el-button>
            </div>
          </div>
        </el-form-item>
        
        <!-- 执行操作 -->
        <el-form-item label="执行操作" prop="actions">
          <div class="action-list">
            <div v-for="(action, index) in ruleForm.actions" :key="index" class="action-item-form">
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-select v-model="action.type" placeholder="选择操作" @change="handleActionTypeChange(index)">
                    <el-option label="移动到文件夹" value="moveToFolder"></el-option>
                    <el-option label="标记为已读" value="markAsRead"></el-option>
                    <el-option label="标记为星标" value="markAsStarred"></el-option>
                    <el-option label="转发到" value="forwardTo"></el-option>
                    <el-option label="删除邮件" value="delete"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="14" v-if="action.type === 'moveToFolder'">
                  <el-select v-model="action.folder" placeholder="选择文件夹">
                    <el-option label="收件箱" value="inbox"></el-option>
                    <el-option label="已发送" value="sent"></el-option>
                    <el-option label="垃圾邮件" value="spam"></el-option>
                    <el-option label="已删除" value="trash"></el-option>
                    <el-option label="草稿箱" value="draft"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="14" v-if="action.type === 'forwardTo'">
                  <el-input v-model="action.email" placeholder="输入转发邮箱"></el-input>
                </el-col>
                <el-col :span="3" class="action-actions">
                  <el-button 
                    type="text" 
                    icon="el-icon-delete" 
                    @click="removeAction(index)"
                    :disabled="ruleForm.actions.length <= 1">
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <div class="add-action">
              <el-button type="text" icon="el-icon-plus" @click="addAction">添加操作</el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-input-number v-model="ruleForm.priority" :min="1" :max="100"></el-input-number>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="ruleForm.status"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import XrHeader from '@/components/XrHeader'

export default {
  name: 'SendRules',
  components: {
    XrHeader
  },
  data() {
    return {
      loading: false,
      rulesList: [],
      filteredRulesList: [],
      filterForm: {
        ruleType: '',
        creator: '',
        keyword: ''
      },
      dialogVisible: false,
      dialogTitle: '新建规则',
      editingRuleId: null,
      ruleForm: {
        ruleName: '',
        ruleType: 'receive',
        conditions: [{
          field: 'sender',
          operator: 'contains',
          value: ''
        }],
        actions: [{
          type: 'moveToFolder',
          folder: 'inbox'
        }],
        priority: 1,
        status: true,
        creator: '管理员'
      },
      rules: {
        ruleName: [
          { required: true, message: '请输入规则名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        ruleType: [
          { required: true, message: '请选择规则类型', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.fetchRulesList()
  },
  
  computed: {
    // 根据筛选条件过滤规则列表
    filteredList() {
      return this.filteredRulesList.length > 0 ? this.filteredRulesList : this.rulesList
    }
  },
  methods: {
    // 获取规则列表
    fetchRulesList() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.rulesList = [
          {
            id: 1,
            ruleName: '垃圾邮件过滤',
            ruleType: 'receive',
            conditions: [
              { field: 'sender', operator: 'contains', value: 'promotion' },
              { field: 'subject', operator: 'contains', value: '优惠' }
            ],
            actions: [
              { type: 'moveToFolder', folder: 'spam' }
            ],
            priority: 1,
            status: true,
            creator: '管理员'
          },
          {
            id: 2,
            ruleName: '重要客户邮件',
            ruleType: 'receive',
            conditions: [
              { field: 'sender', operator: 'contains', value: '<EMAIL>' }
            ],
            actions: [
              { type: 'markAsStarred' },
              { type: 'forwardTo', email: '<EMAIL>' }
            ],
            priority: 2,
            status: true,
            creator: '系统'
          },
          {
            id: 3,
            ruleName: '自动回复',
            ruleType: 'send',
            conditions: [
              { field: 'recipient', operator: 'contains', value: '<EMAIL>' }
            ],
            actions: [
              { type: 'markAsRead' }
            ],
            priority: 3,
            status: true,
            creator: '管理员'
          }
        ]
        this.filteredRulesList = []
        this.loading = false
      }, 500)
    },
    
    // 处理筛选
    handleFilter() {
      const { ruleType, creator, keyword } = this.filterForm
      
      if (!ruleType && !creator && !keyword) {
        this.filteredRulesList = []
        return
      }
      
      this.filteredRulesList = this.rulesList.filter(rule => {
        let matchType = true
        let matchCreator = true
        let matchKeyword = true
        
        if (ruleType) {
          matchType = rule.ruleType === ruleType
        }
        
        if (creator) {
          matchCreator = rule.creator === creator
        }
        
        if (keyword) {
          const lowerKeyword = keyword.toLowerCase()
          matchKeyword = rule.ruleName.toLowerCase().includes(lowerKeyword)
        }
        
        return matchType && matchCreator && matchKeyword
      })
    },
    
    // 获取规则类型名称
    getRuleTypeName(type) {
      const typeMap = {
        'receive': '收件规则',
        'send': '发件规则'
      }
      return typeMap[type] || type
    },
    
    // 获取条件文本描述
    getConditionText(condition) {
      const fieldMap = {
        'sender': '发件人',
        'recipient': '收件人',
        'subject': '主题',
        'content': '内容'
      }
      
      const operatorMap = {
        'contains': '包含',
        'notContains': '不包含',
        'equals': '等于',
        'startsWith': '开头是',
        'endsWith': '结尾是'
      }
      
      return `${fieldMap[condition.field] || condition.field} ${operatorMap[condition.operator] || condition.operator} "${condition.value}"`
    },
    
    // 获取操作文本描述
    getActionText(action) {
      const actionMap = {
        'moveToFolder': '移动到文件夹',
        'markAsRead': '标记为已读',
        'markAsStarred': '标记为星标',
        'forwardTo': '转发到',
        'delete': '删除邮件'
      }
      
      const folderMap = {
        'inbox': '收件箱',
        'sent': '已发送',
        'spam': '垃圾邮件',
        'trash': '已删除',
        'draft': '草稿箱'
      }
      
      if (action.type === 'moveToFolder') {
        return `${actionMap[action.type]} ${folderMap[action.folder] || action.folder}`
      } else if (action.type === 'forwardTo') {
        return `${actionMap[action.type]} ${action.email}`
      } else {
        return actionMap[action.type] || action.type
      }
    },
    
    // 处理状态变更
    handleStatusChange(val, row) {
      console.log(`规则 ${row.ruleName} 状态变更为: ${val ? '启用' : '禁用'}`)
      // 这里应该调用API更新规则状态
    },
    
    // 处理添加规则
    handleAddRule() {
      this.dialogTitle = '新建规则'
      this.editingRuleId = null
      this.resetForm()
      this.dialogVisible = true
    },
    
    // 处理编辑规则
    handleEdit(row) {
      this.dialogTitle = '编辑规则'
      this.editingRuleId = row.id
      
      // 深拷贝规则数据
      this.ruleForm = JSON.parse(JSON.stringify({
        ruleName: row.ruleName,
        ruleType: row.ruleType,
        conditions: row.conditions,
        actions: row.actions,
        priority: row.priority,
        status: row.status
      }))
      
      this.dialogVisible = true
    },
    
    // 处理删除规则
    handleDelete(row) {
      this.$confirm(`确认删除规则 "${row.ruleName}"?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟API调用
        const index = this.rulesList.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.rulesList.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {
        // 取消删除
      })
    },
    
    // 处理上移规则
    handleMoveUp(index) {
      if (index > 0) {
        const temp = this.rulesList[index]
        this.$set(this.rulesList, index, this.rulesList[index - 1])
        this.$set(this.rulesList, index - 1, temp)
        
        // 更新优先级
        this.updatePriorities()
      }
    },
    
    // 处理下移规则
    handleMoveDown(index) {
      if (index < this.rulesList.length - 1) {
        const temp = this.rulesList[index]
        this.$set(this.rulesList, index, this.rulesList[index + 1])
        this.$set(this.rulesList, index + 1, temp)
        
        // 更新优先级
        this.updatePriorities()
      }
    },
    
    // 更新优先级
    updatePriorities() {
      this.rulesList.forEach((rule, index) => {
        rule.priority = index + 1
      })
    },
    
    // 添加条件
    addCondition() {
      this.ruleForm.conditions.push({
        field: 'sender',
        operator: 'contains',
        value: ''
      })
    },
    
    // 移除条件
    removeCondition(index) {
      if (this.ruleForm.conditions.length > 1) {
        this.ruleForm.conditions.splice(index, 1)
      }
    },
    
    // 添加操作
    addAction() {
      this.ruleForm.actions.push({
        type: 'moveToFolder',
        folder: 'inbox'
      })
    },
    
    // 移除操作
    removeAction(index) {
      if (this.ruleForm.actions.length > 1) {
        this.ruleForm.actions.splice(index, 1)
      }
    },
    
    // 处理操作类型变更
    handleActionTypeChange(index) {
      const action = this.ruleForm.actions[index]
      if (action.type === 'moveToFolder') {
        this.$set(action, 'folder', 'inbox')
        // 移除其他可能的属性
        delete action.email
      } else if (action.type === 'forwardTo') {
        this.$set(action, 'email', '')
        // 移除其他可能的属性
        delete action.folder
      } else {
        // 移除所有额外属性
        delete action.folder
        delete action.email
      }
    },
    
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 验证条件和操作是否有值
          const hasEmptyCondition = this.ruleForm.conditions.some(condition => !condition.value)
          if (hasEmptyCondition) {
            this.$message.error('条件值不能为空')
            return
          }
          
          const hasEmptyForwardEmail = this.ruleForm.actions.some(action => 
            action.type === 'forwardTo' && !action.email
          )
          if (hasEmptyForwardEmail) {
            this.$message.error('转发邮箱不能为空')
            return
          }
          
          // 保存规则
          if (this.editingRuleId) {
            // 编辑现有规则
            const index = this.rulesList.findIndex(rule => rule.id === this.editingRuleId)
            if (index !== -1) {
              this.$set(this.rulesList, index, {
                ...this.rulesList[index],
                ...this.ruleForm
              })
              this.$message.success('规则更新成功')
            }
          } else {
            // 添加新规则
            const newRule = {
              id: Date.now(), // 使用时间戳作为临时ID
              ...this.ruleForm
            }
            this.rulesList.push(newRule)
            this.$message.success('规则添加成功')
          }
          
          this.dialogVisible = false
        } else {
          return false
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.ruleForm = {
        ruleName: '',
        ruleType: 'receive',
        conditions: [{
          field: 'sender',
          operator: 'contains',
          value: ''
        }],
        actions: [{
          type: 'moveToFolder',
          folder: 'inbox'
        }],
        priority: this.rulesList.length + 1,
        status: true
      }
    },
    
    // 处理对话框关闭
    handleDialogClose() {
      this.$refs.ruleForm && this.$refs.ruleForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.send-rules-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .main-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f5f7fa;
  }
  
  .rules-section {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .section-title {
        font-size: 16px;
        font-weight: bold;
      }
    }
    
    .filter-section {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
    }
    
    .rules-table {
      margin-bottom: 20px;
      
      .condition-item, .action-item {
        margin-bottom: 5px;
        line-height: 1.5;
      }
      
      .delete-btn {
        color: #f56c6c;
      }
    }
  }
  
  .empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    
    .empty-img {
      width: 100px;
      margin-bottom: 10px;
    }
    
    .empty-text {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .condition-item-form, .action-item-form {
    margin-bottom: 10px;
  }
  
  .add-condition, .add-action {
    margin-top: 10px;
  }
  
  .condition-actions, .action-actions {
    display: flex;
    align-items: center;
  }
}
</style>