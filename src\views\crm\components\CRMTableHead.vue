<template>
  <div class="table-head-container">
    <wk-filter-header
      :tabs="sceneList"
      :props="filterHeaderProps"
      :active-tab="sceneSelectId"
      :selection-list="selectionList"
      :scene-show="showSceneView"
      :handles="handles"
      @tabs-change="sceneSelect"
      @command="selectionBarClick"
      @on-handle="filterHeaderHandle"
    >
      <template slot="left-start">
        <slot name="custom" />
      </template>
      <template slot="right">
        <el-button
          v-if="exportFields.length > 0"
          class="collapse-button"
          :type="showExportFields ? 'selected' : 'text'"
          @click="collapseHandle()">
          {{ showExportFields ? '收起筛选' : '展开筛选' }}
          <i :class="['el-icon-arrow-up', { 'is-reverse' : !showExportFields}]" />
        </el-button>
        <el-button
          v-if="showFilterView"
          type="subtle"
          class="filter-button"
          :class="{'has-values': filterValidCount > 0}"
          icon="wk wk-screening"
          @click="showFilterClick">高级筛选<span
            v-if="filterValidCount > 0"
            class="values-span"
            closable
            type="info">
            {{ filterValidCount }}<i class="el-icon-close" @click.stop="clearFilterVal(filterObj.form)" />
          </span></el-button>
        <!-- <el-button
          v-if="sortData && sortData.order && sortData.column"
          type="primary"
          plain
          @click="handleCallBack({type: 'clear-sort'})">
          {{ `${sortData.column.label}${{ascending: '升序', descending: '降序'}[sortData.order]}` }}<i style="margin-left: 5px;" class="el-icon-close"/>
        </el-button> -->
        <slot />
      </template>
    </wk-filter-header>

    <!-- <flexbox
      v-if="selectionList.length > 0"
      class="selection-bar">
      <div class="selected—title">已选中 <span class="selected—count">{{ selectionList.length }}</span> 项</div>
      <flexbox class="selection-items-box">
        <el-button
          v-for="(item, index) in getSelectionHandleItemsInfo()"
          v-show="whetherTypeShowByPermision(item.type)"
          :icon="item.icon | wkIconPre"
          :key="index"
          size="medium"
          @click="selectionBarClick(item.type)">{{ item.name }}</el-button>
      </flexbox>
    </flexbox> -->

    <filter-export-fields
      v-if="exportFields.length > 0 && showExportFields"
      :crm-type="crmType"
      :form="exportFields"
      :field-list="fieldList"
      @filter="exportFilter"
      @clear="clearFilterVal"
    />

    <!-- <filter-content
      v-if="filterObj.form && filterObj.form.length > 0"
      :obj="filterObj"
      @delete="handleDeleteField" /> -->

    <transfer-handle
      v-if="transferDialogShow"
      :crm-type="crmType"
      :selection-list="selectionList"
      :dialog-visible.sync="transferDialogShow"
      @handle="handleCallBack" />
    <teams-handle
      v-if="teamsDialogShow"
      :crm-type="crmType"
      :type="teamsType"
      :selection-list="selectionList"
      :dialog-visible.sync="teamsDialogShow"
      @handle="handleCallBack" />
    <alloc-handle
      :crm-type="crmType"
      :pool-id="poolId"
      :selection-list="selectionList"
      :dialog-visible.sync="allocDialogShow"
      @handle="handleCallBack" />
    <deal-status-handle
      :crm-type="crmType"
      :selection-list="selectionList"
      :visible.sync="dealStatusShow"
      @handle="handleCallBack" />

    <scene-set
      :dialog-visible.sync="showSceneSet"
      :crm-type="crmType"
      @save-success="updateSceneList" />

    <scene-create
      :field-list="fieldList"
      :crm-type="crmType"
      :dialog-visible.sync="showSceneCreate"
      :obj="sceneFilterObj"
      @save-success="updateSceneList" />

    <put-pool-handle
      :visible.sync="putPoolShow"
      :selection-list="selectionList"
      @handle="handleCallBack" />

    <filter-form
      v-if="showFilterView"
      :field-list="fieldList"
      :dialog-visible.sync="showFilter"
      :obj="filterObj"
      :crm-type="crmType"
      :save-scene="!isSeas"
      @filter="handleFilter" />
  </div>
</template>

<script type="text/javascript">
import { mapGetters } from 'vuex'
import crmTypeModel from '@/views/crm/model/crmTypeModel'
import {
  filterIndexfieldsAPI,
  crmSceneSaveAPI
} from '@/api/crm/common'
import {
  crmWeixinDeleteAPI,
  crmWeixinLeadsExportLeadsAPI,
  crmWeixinChangeLeadsAPI
} from '@/api/crm/applet'
import {
  crmLeadsTransformAPI,
  crmLeadsExcelExportAPI,
  crmLeadsDeleteAPI
} from '@/api/crm/leads'
import {
  crmCustomerLockAPI,
  crmCustomerExcelExportAPI,
  crmCustomerPoolExcelExportAPI,
  crmCustomerPoolQueryPoolFieldAPI,
  crmCustomerDeleteAPI,
  crmCustomerPoolDeleteAPI,
  crmCustomerReceiveAPI
} from '@/api/crm/customer'
import {
  crmContactsDeleteAPI,
  crmContactsExcelExportAPI
} from '@/api/crm/contacts'
import {
  crmBusinessDeleteAPI,
  crmBusinessExcelExportAPI
} from '@/api/crm/business'
import {
  crmContractDeleteAPI,
  crmContractExcelExportAPI
} from '@/api/crm/contract'
import {
  crmReceivablesDeleteAPI,
  crmReceivablesExcelExportAPI
} from '@/api/crm/receivables'
import {
  crmReceivablesPlanDeleteAPI,
  crmReceivablesPlanExcelExportAPI
} from '@/api/crm/receivablesPlan'
import {
  crmProductStatusAPI,
  crmProductExcelExportAPI,
  crmProductDeleteAPI
} from '@/api/crm/product'
import {
  crmMarketingIsEnableAPI,
  crmMarketingDeleteAPI
} from '@/api/crm/marketing'
import {
  crmReturnVisitDeleteAPI
} from '@/api/crm/visit'

import FilterForm from './FilterForm'
import FilterExportFields from './FilterForm/FilterExportFields'
import SceneSet from './SceneForm/SceneSet' // 场景设置
import SceneCreate from './SceneForm/SceneCreate'

import TransferHandle from '@/components/Page/SelectionHandle/TransferHandle' // 转移
import TeamsHandle from '@/components/Page/SelectionHandle/TeamsHandle' // 操作团队成员
import AllocHandle from './SelectionHandle/AllocHandle' // 公海分配操作
import DealStatusHandle from './SelectionHandle/DealStatusHandle' // 客户状态修改操作
import PutPoolHandle from './SelectionHandle/PutPoolHandle' // 放入公海

import { Loading } from 'element-ui'
// import { downloadExcelWithResData } from '@/utils'
import WkFilterHeader from '@/components/NewCom/WkFilterHeader'
import FilterMixin from '../mixins/Filter'
import { isArray } from '@/utils/types'
import AdvancedFilterMixin from '@/mixins/AdvancedFilter'
import { objDeepCopy } from '@/utils'

export default {
  name: 'CRMTableHead', // 客户管理下 重要提醒 回款计划提醒
  components: {
    FilterForm,
    FilterExportFields,
    TransferHandle,
    TeamsHandle,
    AllocHandle,
    SceneCreate,
    SceneSet,
    DealStatusHandle,
    PutPoolHandle,
    WkFilterHeader
  },
  mixins: [FilterMixin, AdvancedFilterMixin],
  props: {
    title: {
      type: String,
      default: ''
    },
    /** 没有值就是全部类型 有值就是当个类型 */
    crmType: {
      type: String,
      default: ''
    },
    // 辅助 使用 公海没有场景
    isSeas: {
      type: Boolean,
      default: false
    },
    poolId: [String, Number],
    // 公海权限
    poolAuth: Object,
    // 排序信息
    sortData: Object,
    // 自定义操作
    handleFun: Function,
    // 搜索
    showSearch: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '内容'
    },
    // 详细控制属性  同 Table.js 里的 DefaultFilterProps
    props: Object
  },
  data() {
    return {
      searchInputUnfold: true,
      loading: false, // loading
      loadingObj: null, // loading 对象
      sceneType: null,
      showFilter: false, // 控制筛选框
      showExportFields: false, // 展示外露筛选
      fieldList: [],
      filterObj: { form: [], obj: [] }, // 筛选确定数据 form 是展示数组  obj 是上传数据
      sceneData: { id: '', bydata: '', name: '' },
      showSceneSet: false, // 展示场景设置
      showSceneCreate: false, // 展示场景添加
      sceneFilterObj: { form: [] }, // 筛选确定数据

      /** 勾选操作数据 */
      selectionList: [],
      transferDialogShow: false,
      teamsDialogShow: false, // 团队操作提示框
      teamsType: '', // 团队操作类型
      allocDialogShow: false, // 公海分配操作提示框
      dealStatusShow: false, // 成交状态修改框
      putPoolShow: false // 客户放入公海

    }
  },
  computed: {
    ...mapGetters(['crm']),
    sceneName() {
      return this.sceneData.name || '全部'
    },
    // 展示场景
    showSceneView() {
      if (this.props) {
        return this.props.showScene
      }

      if (this.isSeas || ['marketing', 'applet'].includes(this.crmType)) {
        return false
      } else {
        return true
      }
    },

    // 展示筛选
    showFilterView() {
      if (['marketing', 'applet'].includes(this.crmType)) {
        return false
      } else if (this.isSeas) {
        return !!this.poolId
      } else {
        return true
      }
    },

    // 外露搜索字段
    exportFields() {
      if (this.filterObj.form) {
        return this.filterObj.form.filter(item => item.isOut === 1)
      }
      return []
    },

    // 场景最大展示数量
    sceneMaxCount() {
      // 是选择数据时，因为是弹窗最大展示2个
      return this.$parent.config.isSelect ? 2 : 5
    },

    // 控制tab 和 搜索占位
    filterHeaderProps() {
      return {
        maxTabCount: this.sceneMaxCount,
        searchPlaceholder: this.placeholder
      }
    },

    // 有条件的值
    filterValidCount() {
      const conditions = isArray(this.filterObj.obj) ? this.filterObj.obj : []
      // 滤掉无效值 5 6 为空 不为空 不需要提供values
      const hasValueConditions = conditions.filter(item => (item.values && item.values.length > 0) || ([5, 6].includes(item.type)))
      return hasValueConditions.length
    },

    // 勾选可执行的操作
    handles() {
      return this.getSelectionHandleItemsInfo()
    }
  },
  watch: {
    loading(val) {
      if (val) {
        this.loadingObj = Loading.service({
          target: document.querySelector('#crm-main-container')
        })
      } else {
        this.loadingObj && this.loadingObj.close()
      }
    },

    '$parent.customConfig': {
      handler(newVal, oldVal) {
        if (oldVal && Object.keys(oldVal).length === 0) {
          const { filterConfig } = newVal
          if (filterConfig) {
            this.showExportFields = filterConfig.showExportFields || false
          }
        }
      }
    },

    // 仅为了公海筛选必须有poolId
    poolId: {
      handler(newVal) {
        if (newVal) {
          this.getFilterFieldInfo(false)
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.showFilterView && this.$auth(`crm.${this.crmType}.index`)) {
      this.getFilterFieldInfo(false)
    }
  },
  methods: {
    /**
     * filter header 操作
     */
    filterHeaderHandle(commond, value) {
      if (commond === 'sceneSet') {
        this.showSceneSet = true
      } else if (commond === 'search') {
        this.$emit('on-search', value)
      } else if (commond === 'searchUpdate') {
        this.$emit('update:search', value)
      }
    },

    // /** 发布 时候的类型选择 */
    // handleTypeDrop(command) {
    //   this.sceneType = command
    // },

    /**
     * 展示高级筛选
     */
    showFilterClick() {
      this.getFilterFieldInfo(true)
    },

    /**
     * 获取高级筛选字段数据
     */
    getFilterFieldInfo(showFilter = false) {
      const params = {}
      if (this.isSeas) {
        params.poolId = this.poolId
      } else {
        params.label = crmTypeModel[this.crmType]
      }

      const request = this.isSeas ? crmCustomerPoolQueryPoolFieldAPI : filterIndexfieldsAPI

      request(params)
        .then(res => {
          const resData = res.data
          if (this.props && this.props.ignoreFilterFields) {
            const { ignoreFilterFields } = this.props
            this.fieldList = resData.filter(item => !ignoreFilterFields.includes(item.fieldName))
          } else {
            this.fieldList = resData
          }

          // 查询外露条件
          if (!showFilter) {
            const exportFields = []
            this.fieldList.forEach(item => {
              if (item.isOut === 1) {
                const copyItem = objDeepCopy(item)
                exportFields.push(this.getAdvancedFilterDefaultItemByFormType(copyItem))
              }
            })
            this.filterObj.form = exportFields
          }
          this.showFilter = showFilter
        })
        .catch(() => {})
    },

    /**
     * 高级筛选确定
     * isExport 是外露点击 不发送请求
     */
    handleFilter(form, isExport) {
      this.filterObj = form
      if (isExport) {
        if (!this.showExportFields) {
          this.collapseHandle(true)
        }
      } else {
        this.showFilter = false
        if (form.saveChecked) {
          crmSceneSaveAPI({
            type: crmTypeModel[this.crmType],
            isDefault: form.saveDefault ? 1 : 0,
            name: form.saveName,
            data: JSON.stringify(form.obj)
          })
            .then(res => {
              this.updateSceneList()
            })
            .catch(() => {})
        }
        this.$emit('filter', form.obj)
      }
    },
    // // 删除
    // handleDeleteField(data) {
    //   this.filterObj = data.obj
    //   this.$emit('filter', this.filterObj.obj)
    // },

    /**
     * 外露筛选
     */
    exportFilter(data) {
      // 使用外露筛选  更新 obj 里的内容， form 的内容会双向联动
      this.filterObj.obj = data
      this.$emit('filter', data)
    },

    /**
     * 清空外露筛选值
     */
    clearFilterVal(list) {
      this.filterObj.obj = []
      // 下面方法 重置 form里的值
      this.resetAdvancedFilterFieldsValue(list)
      this.$emit('filter', [])
    },
    /**
      * 外露筛选折叠操作
      */
    collapseHandle(show) {
      this.showExportFields = show !== undefined ? show : !this.showExportFields
      //  true：是折叠不请求数据 更新table高度
      this.$emit('filter', {}, true)
      this.updateFilterConfig()
    },

    /**
     * 更新style样式
     */
    updateFilterConfig() {
      this.$parent.updateTableStyle({
        filterConfig: {
          showExportFields: this.showExportFields
        }
      })
    },

    // 场景操作
    /**
     * 选择了场景
     */
    sceneSelect(data) {
      this.sceneData = data
      this.$emit('scene', data)
    },
    sceneHandle(data) {
      if (data.type == 'set') {
        this.showSceneSet = true
      } else if (data.type == 'add') {
        filterIndexfieldsAPI({
          label: crmTypeModel[this.crmType]
        })
          .then(res => {
            this.fieldList = res.data
            this.showSceneCreate = true
          })
          .catch(() => {})
      }
    },
    /**
     * 创建保存成功
     */
    updateSceneList() {
      this.getSceneList()
    },

    /**
     * 勾选后的表头操作
     */

    headSelectionChange(array) {
      this.selectionList = array
    },

    /**
     * 操作
     */
    selectionBarClick(type) {
      // action 动作 handle 偏向结果
      this.$emit('action', { type: type, selection: this.selectionList })

      // 传出selection操作
      if (this.handleFun) {
        this.handleFun(type)
        return
      }

      if (type == 'transfer') {
        // 转移
        this.transferDialogShow = true
      } else if (type == 'export') {
        const params = {}
        let request = null
        if (this.isSeas) {
          request = crmCustomerPoolExcelExportAPI
          params.poolId = this.poolId
          params.ids = this.selectionList
            .map(item => item.customerId)
        } else if (this.crmType === 'applet') {
          request = crmWeixinLeadsExportLeadsAPI
          params.ids = this.selectionList
            .map(item => item.weixinLeadsId)
            .join(',')
        } else {
          request = {
            customer: crmCustomerExcelExportAPI,
            leads: crmLeadsExcelExportAPI,
            contacts: crmContactsExcelExportAPI,
            business: crmBusinessExcelExportAPI,
            contract: crmContractExcelExportAPI,
            receivables: crmReceivablesExcelExportAPI,
            receivablesPlan: crmReceivablesPlanExcelExportAPI,
            product: crmProductExcelExportAPI
          }[this.crmType]
          params.ids = this.selectionList
            .map(item => item[`${this.crmType}Id`])
        }
        this.$wkExport.export(this.crmType, {
          params,
          request,
          isSeas: this.isSeas,
          poolId: this.poolId
        })
        // request(params)
        //   .then(res => {
        //     downloadExcelWithResData(res)
        //   })
        //   .catch(() => {})
      } else if (
        type == 'transform' ||
        type == 'delete' ||
        type == 'lock' ||
        type == 'unlock' ||
        type == 'start' ||
        type == 'disable' ||
        type == 'transformLead' ||
        type == 'state_start' ||
        type == 'state_disable' ||
        type == 'get'
      ) {
        var message = ''
        if (type == 'transform') {
          message = '确定将这些线索转换为客户吗?'
        } else if (type == 'delete') {
          message = this.isSeas ? '若客户下有联系人，联系人将一并删除。确定删除？' : `确定删除选中的${this.selectionList.length}项吗？`
        } else if (type == 'lock') {
          message = '确定要锁定这些客户吗？锁定后将不会掉入公海。'
        } else if (type == 'unlock') {
          message = '确定要解锁这些客户吗？'
        } else if (type == 'start') {
          message = '确定要上架这些产品吗?'
        } else if (type == 'disable') {
          message = '确定要下架这些产品吗?'
        } else if (type == 'state_start') {
          message = '确定要启用这些活动吗?'
        } else if (type == 'state_disable') {
          message = '确定要停用这些活动吗?'
        } else if (type == 'get') {
          message = '确定要领取该客户吗?'
        } else if (type === 'transformLead') {
          message = '确定将这些名片线索转化为线索吗?'
        }
        this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.confirmHandle(type)
          })
          .catch(() => {})
      } else if (type == 'add_user') {
        // 团队操作
        this.teamsType = 'add'
        this.teamsDialogShow = true
      } else if (type == 'delete_user') {
        // 团队操作
        this.teamsType = 'delete'
        this.teamsDialogShow = true
      } else if (type == 'alloc') {
        // 公海分配操作
        this.allocDialogShow = true
      } else if (type == 'deal_status') {
        // 客户成交状态操作
        this.dealStatusShow = true
      } else if (type == 'put_seas') {
        // 客户放入公海
        this.putPoolShow = true
      }
    },
    confirmHandle(type) {
      if (type === 'lock' || type === 'unlock') {
        this.loading = true
        crmCustomerLockAPI({
          status: type === 'lock' ? '2' : '1', // 1是正常 2 是锁定
          ids: this.selectionList.map(item => item.customerId).join(',')
        })
          .then(res => {
            this.loading = false
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.$emit('handle', { type: type })
          })
          .catch(() => {
            this.loading = false
          })
      } else if (type === 'transform') {
        this.loading = true
        crmLeadsTransformAPI(this.selectionList.map(item => item.leadsId))
          .then(res => {
            this.loading = false
            this.$message({
              type: 'success',
              message: '转化成功'
            })
            // 刷新待办
            this.$store.dispatch('GetMessageNum')

            this.$emit('handle', { type: type })
          })
          .catch(() => {
            this.loading = false
          })
      } else if (type === 'start' || type === 'disable') {
        this.loading = true
        crmProductStatusAPI({
          ids: this.selectionList.map(item => item.productId),
          status: type === 'start' ? '1' : '0'
        })
          .then(res => {
            this.loading = false
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.$emit('handle', { type: type })
          })
          .catch(() => {
            this.loading = false
          })
      } else if (type === 'state_start' || type === 'state_disable') {
        this.loading = true
        crmMarketingIsEnableAPI({
          marketingIds: this.selectionList.map(item => item.marketingId).join(','),
          status: type === 'state_start' ? 1 : 0
        })
          .then(res => {
            this.loading = false
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            this.$emit('handle', { type: type })
          })
          .catch(() => {
            this.loading = false
          })
      } else if (type === 'delete') {
        let crmTypes = ''
        if (this.crmType === 'applet') {
          crmTypes = 'weixinLeads'
        } else {
          crmTypes = this.crmType
        }
        const ids = this.selectionList.map(item => item[crmTypes + 'Id'])
        const request = {
          leads: crmLeadsDeleteAPI,
          customer: this.isSeas ? crmCustomerPoolDeleteAPI : crmCustomerDeleteAPI,
          contacts: crmContactsDeleteAPI,
          business: crmBusinessDeleteAPI,
          contract: crmContractDeleteAPI,
          receivables: crmReceivablesDeleteAPI,
          receivablesPlan: crmReceivablesPlanDeleteAPI,
          applet: crmWeixinDeleteAPI,
          marketing: crmMarketingDeleteAPI,
          visit: crmReturnVisitDeleteAPI,
          product: crmProductDeleteAPI
        }[this.crmType]
        const params = {
        }
        if (this.isSeas) {
          params.poolId = this.poolId
        }
        params.ids = ids
        this.loading = true
        request(this.isSeas ? params : ids)
          .then(res => {
            this.loading = false
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.$emit('handle', { type: type })
          })
          .catch(() => {
            // 批量沟通 可能部分删除成功，需要刷新列表
            this.$emit('handle', { type: type })
            this.loading = false
          })
      } else if (type === 'get') {
        // 领取
        this.loading = true
        crmCustomerReceiveAPI({
          ids: this.selectionList.map(item => item.customerId),
          poolId: this.poolId
        })
          .then(res => {
            this.loading = false
            this.$message({
              type: 'success',
              message: '操作成功'
            })
            // 刷新待办
            this.$store.dispatch('GetMessageNum')

            this.$emit('handle', { type: type })
          })
          .catch(() => {
            this.loading = false
          })
      } else if (type === 'transformLead') {
        this.loading = true
        crmWeixinChangeLeadsAPI({
          'weixinLeadsIds': this.selectionList.map(item => item.weixinLeadsId).join(',')
        }).then(res => {
          this.loading = false
          this.$message.success('转化为线索成功')
          this.$emit('handle', { type: type })
        }).catch(() => {
          this.loading = false
        })
      }
    },
    /** 获取展示items */
    getSelectionHandleItemsInfo() {
      const handleInfos = {
        transfer: {
          name: '转移',
          type: 'transfer',
          icon: 'wk wk-icon-transfer2'
        },
        transform: {
          name: '转化为客户',
          type: 'transform',
          icon: 'wk wk-customer-line'
        },
        transformLead: {
          name: '转化为线索',
          type: 'transformLead',
          icon: 'wk wk-leads-line'
        },
        export: {
          name: '导出选中',
          type: 'export',
          icon: 'wk wk-icon-export2'
        },
        delete: {
          name: '删除',
          type: 'delete',
          icon: 'wk wk-icon-delete-line'
        },
        put_seas: {
          name: '放入公海',
          type: 'put_seas',
          icon: 'wk wk-seas'
        },
        lock: {
          name: '锁定',
          type: 'lock',
          icon: 'wk wk-icon-lock2'
        },
        unlock: {
          name: '解锁',
          type: 'unlock',
          icon: 'wk wk-icon-unlock2'
        },
        add_user: {
          name: '添加团队成员',
          type: 'add_user',
          icon: 'wk wk-icon-add-line'
        },
        delete_user: {
          name: '移除团队成员',
          type: 'delete_user',
          icon: 'wk wk-icon-remove-line'
        },
        alloc: {
          name: '分配',
          type: 'alloc',
          icon: 'wk wk-icon-org'
        },
        get: {
          name: '领取',
          type: 'get',
          icon: 'wk wk-activity-line'
        },
        start: {
          name: '上架',
          type: 'start',
          icon: 'wk wk-icon-shelves-line'
        },
        disable: {
          name: '下架',
          type: 'disable',
          icon: 'wk wk-icon-sold-out-line'
        },
        state_start: {
          name: '启用',
          type: 'state_start',
          icon: 'wk wk-icon-success-line'
        },
        state_disable: {
          name: '停用',
          type: 'state_disable',
          icon: 'wk wk-icon-close-line'
        },
        deal_status: {
          name: '更改成交状态',
          type: 'deal_status',
          icon: 'wk wk-icon-success-line'
        },
        reset_invoice_status: {
          name: '重置开票信息',
          type: 'reset_invoice_status',
          icon: 'wk wk-icon-reset2'
        },
        update: {
          name: '编辑',
          type: 'update',
          icon: 'wk wk-icon-edit-line'
        },
        send_email: {
          name: '发邮件',
          type: 'send_email',
          icon: 'wk wk-icon-email2'
        },
        subimt_approve: {
          name: '提交审批',
          type: 'subimt_approve',
          icon: 'wk wk-icon-subtasks-line'
        },
        sync_customer: {
          name: '客户同步ERP',
          type: 'sync_customer',
          icon: 'wk wk-approval-3'
        }
      }
      if (this.crmType == 'leads') {
        return this.forSelectionHandleItems(handleInfos, [
          'transfer',
          'transform',
          'export',
          'delete'
        ])
      } else if (this.crmType == 'customer') {
        if (this.isSeas) {
          return this.forSelectionHandleItems(handleInfos, [
            'alloc',
            'get',
            'export',
            'delete'
          ])
        } else {
          return this.forSelectionHandleItems(handleInfos, [
            'transfer',
            'put_seas',
            'deal_status',
            'export',
            'delete',
            'lock',
            'unlock',
            'add_user',
            'delete_user',
            'send_email'
          ])
        }
      } else if (this.crmType == 'contacts') {
        return this.forSelectionHandleItems(handleInfos, [
          'transfer',
          'export',
          'delete',
          'add_user',
          'delete_user'
        ])
      } else if (this.crmType == 'business') {
        return this.forSelectionHandleItems(handleInfos, [
          'transfer',
          'export',
          'delete',
          'add_user',
          'delete_user'
        ])
      } else if (this.crmType == 'contract') {
        return this.forSelectionHandleItems(handleInfos, [
          'transfer',
          'export',
          'delete',
          'add_user',
          'delete_user',
          'subimt_approve'
        ])
      } else if (this.crmType == 'receivables') {
        return this.forSelectionHandleItems(handleInfos, [
          'transfer',
          'export',
          'delete',
          'add_user',
          'delete_user'
        ])
      } else if (this.crmType == 'receivablesPlan') {
        return this.forSelectionHandleItems(handleInfos, [
          'export',
          'update',
          'delete'
        ])
      } else if (this.crmType == 'product') {
        return this.forSelectionHandleItems(handleInfos, [
          'transfer',
          'export',
          'delete',
          'start',
          'disable'
        ])
      } else if (this.crmType === 'applet') {
        return this.forSelectionHandleItems(handleInfos, [
          'export',
          'delete',
          'transformLead'
        ])
      } else if (this.crmType == 'product') {
        return this.forSelectionHandleItems(handleInfos, [
          'export',
          'start',
          'disable'
        ])
      } else if (this.crmType == 'marketing') {
        return this.forSelectionHandleItems(handleInfos, [
          'state_start',
          'state_disable',
          'delete'
        ])
      } else if (this.crmType == 'visit') {
        return this.forSelectionHandleItems(handleInfos, [
          'delete'
        ])
      } else if (this.crmType == 'invoice') {
        return this.forSelectionHandleItems(handleInfos, [
          'delete',
          'reset_invoice_status',
          'transfer'
        ])
      }
    },
    forSelectionHandleItems(handleInfos, array) {
      const tempsHandles = []
      for (let index = 0; index < array.length; index++) {
        const type = array[index]
        if (this.whetherTypeShowByPermision(type)) {
          tempsHandles.push(handleInfos[type])
        }
      }
      return tempsHandles
    },
    // 判断是否展示
    whetherTypeShowByPermision(type) {
      if (!this.crm || !this.crm[this.crmType]) {
        return
      }
      if (type == 'transfer') {
        return this.sceneData.bydata == 'transform'
          ? false
          : this.crm[this.crmType].transfer
      } else if (type == 'transform') {
        return this.sceneData.bydata == 'transform'
          ? false
          : this.crm[this.crmType].transform
      } else if (type == 'export') {
        if (this.isSeas) {
          if (this.poolId) {
            return this.poolAuth.excelexport
          }
          return this.crm.pool && this.crm.pool.excelexport
        }
        if (this.crm[this.crmType]) {
          return this.crm[this.crmType].excelexport
        } else {
          return true
        }
      } else if (type == 'delete') {
        if (this.isSeas) {
          if (this.poolId) {
            return this.poolAuth.delete
          }
          return this.crm.pool && this.crm.pool.delete
        }
        return this.crm[this.crmType].delete
      } else if (type == 'put_seas') {
        // 放入公海(客户)
        return this.crm[this.crmType].putinpool
      } else if (type == 'lock' || type == 'unlock') {
        // 锁定解锁(客户)
        return this.crm[this.crmType].lock
      } else if (type == 'add_user' || type == 'delete_user') {
        // 添加 移除团队成员
        return this.crm[this.crmType].teamsave
      } else if (type == 'alloc') {
        // 分配(公海)
        if (this.poolId) {
          return this.poolAuth.distribute
        }
        return this.crm.pool && this.crm.pool.distribute
      } else if (type == 'get') {
        // 领取(公海)
        if (this.poolId) {
          return this.poolAuth.receive
        }
        return this.crm.pool && this.crm.pool.receive
      } else if (type == 'start') {
        // 上架 下架(产品)
        for (let index = 0; index < this.selectionList.length; index++) {
          const element = this.selectionList[index]
          if (element.status == 1) {
            return false
          }
        }
        return this.crm[this.crmType].status
      } else if (type == 'disable') {
        // 上架 下架(产品)
        for (let index = 0; index < this.selectionList.length; index++) {
          const element = this.selectionList[index]
          if (element.status == 0) {
            return false
          }
        }
        return this.crm[this.crmType].status
      } else if (type == 'deal_status') {
        // 客户状态修改
        return this.crm[this.crmType].dealStatus
      } else if (type === 'transformLead') {
        return true
      } else if (type == 'state_start' || type == 'state_disable') {
        // 活动停用/启用
        return this.crm[this.crmType].updateStatus
      } else if (type == 'reset_invoice_status') {
        // 重置开票信息
        return this.crm[this.crmType].resetInvoiceStatus && this.selectionList.length == 1
      } else if (type == 'update') {
        // 编辑
        return this.crm[this.crmType].update && this.selectionList.length == 1
      }

      return true
    },
    // 子组件 回调的 结果
    handleCallBack(data) {
      this.$emit('handle', { type: data.type })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-head-container {
  margin-bottom: 20px;
  line-height: 32px;
}

.th-container {
  .th-left {
    flex: 1 1 0%;
  }

  .search-input {
    width: 120px;
    transition: all 0.3s cubic-bezier(0.15, 1, 0.3, 1) 0s;
  }

  .search-input.is-unfold,
  .scene-list {
    width: 220px;
  }

  .search-input + .scene-list {
    margin-left: 10px;
  }

  .filter-button {
    float: right;
  }
}

// 有值
.el-button {
  &.has-values {
    background-color: $--color-n30;
  }
}

.values-span {
  margin-left: 2px;
  color: $--color-primary;
  background-color: $--color-n40;
  border-radius: $--border-radius-base;

  .el-icon-close {
    margin-right: 2px;
    font-size: 12px;
    color: $--color-black;
    cursor: pointer;

    &:hover {
      color: $--color-primary;
    }
  }
}

.collapse-button {
  padding-right: 12px;
  padding-left: 12px;

  .el-icon-arrow-up {
    transition: transform 0.3s;
  }

  .el-icon-arrow-up.is-reverse {
    transform: rotate(180deg);
  }
}

/** 勾选操作 */
.selection-bar {
  .selected—title {
    flex-shrink: 0;
    padding-right: 20px;
    font-weight: $--font-weight-semibold;

    .selected—count {
      color: $--color-primary;
    }
  }
}

.selection-items-box {
  padding: 0 15px;
  overflow-x: auto;
  overflow-y: hidden;

  ::v-deep .el-button {
    i {
      margin-right: 5px;
    }
  }
}

// 外露筛选
.filter-export-fields {
  margin-top: 8px;
}
</style>
