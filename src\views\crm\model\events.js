export default {
  transfer: {
    name: '转移',
    type: 'transfer',
    icon: 'wk wk-icon-transfer2'
  },
  transform: {
    name: '转化为客户',
    type: 'transform',
    icon: 'wk wk-customer-line'
  },
  transformLead: {
    name: '转化为线索',
    type: 'transformLead',
    icon: 'wk wk-leads-line'
  },
  export: {
    name: '导出选中',
    type: 'export',
    icon: 'wk wk-icon-export2'
  },
  delete: {
    name: '删除',
    type: 'delete',
    icon: 'wk wk-icon-delete-line'
  },
  put_seas: {
    name: '放入公海',
    type: 'put_seas',
    icon: 'wk wk-seas'
  },
  lock: {
    name: '锁定',
    type: 'lock',
    icon: 'wk wk-icon-lock2'
  },
  unlock: {
    name: '解锁',
    type: 'unlock',
    icon: 'wk wk-icon-unlock2'
  },
  add_user: {
    name: '添加团队成员',
    type: 'add_user',
    icon: 'wk wk-icon-add-line'
  },
  delete_user: {
    name: '移除团队成员',
    type: 'delete_user',
    icon: 'wk wk-icon-remove-line'
  },
  alloc: {
    name: '分配',
    type: 'alloc',
    icon: 'wk wk-icon-org'
  },
  get: {
    name: '领取',
    type: 'get',
    icon: 'wk wk-activity-line'
  },
  start: {
    name: '上架',
    type: 'start',
    icon: 'wk wk-icon-shelves-line'
  },
  disable: {
    name: '下架',
    type: 'disable',
    icon: 'wk wk-icon-sold-out-line'
  },
  state_start: {
    name: '启用',
    type: 'state_start',
    icon: 'wk wk-icon-success-line'
  },
  state_disable: {
    name: '停用',
    type: 'state_disable',
    icon: 'wk wk-icon-close-line'
  },
  deal_status: {
    name: '更改成交状态',
    type: 'deal_status',
    icon: 'wk wk-icon-success-line'
  },
  reset_invoice_status: {
    name: '重置开票信息',
    type: 'reset_invoice_status',
    icon: 'wk wk-icon-reset2'
  },
  update: {
    name: '编辑',
    type: 'update',
    icon: 'wk wk-icon-edit-line'
  },
  send_email: {
    name: '发邮件',
    type: 'send_email',
    icon: 'wk wk-icon-email-line'
  },
  subimt_approve: {
    name: '提交审批',
    type: 'subimt_approve',
    icon: 'wk wk-approval-2'
  },
  sync_customer: {
    name: '客户同步ERP',
    type: 'sync_customer',
    icon: 'wk wk-approval-3'
  }
}
