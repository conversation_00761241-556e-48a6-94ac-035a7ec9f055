import request from '@/utils/request'

/**
 * 获取帮助文档菜单列表
 * @param {*} data
 */
export function helpDocMenuAPI(data) {
  return request({
    url: 'helpDoc/queryMenuList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取帮助文档详情
 * @param {*} data
 */
export function helpDocInfoAPI(data) {
  return request({
    url: 'helpDoc/queryDocInfo',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取系统功能介绍菜单列表
 * @param {*} data
 */
export function helpPlatformMenuAPI(data) {
  return request({
    url: 'helpDoc/queryPlatformMenuList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取系统功能介绍内容
 * @param {*} data
 */
export function helpPlatformContentAPI(data) {
  return request({
    url: 'helpDoc/queryPlatformContent',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 提交反馈
 * @param {*} data
 */
export function helpFeedbackAPI(data) {
  return request({
    url: 'helpDoc/submitFeedback',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
