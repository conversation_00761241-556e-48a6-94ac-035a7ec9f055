/**
 * Create by yxk at 2020/6/11
 * 数学运算
 */

/**
 * 两个浮点数求和
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */

const allowedFileTypes = [
  // 文档
  '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt',
  // 图片
  '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
  // 压缩文件
  '.zip', '.rar', '.7z',
  // 其他常见格式
  '.csv', '.json', '.xml', '.html', '.dwg', '.dxf'
]

export function accAdd(num1, num2) {
  // eslint-disable-next-line one-var
  let r1 = 0, r2 = 0
  try {
    r1 = (num1.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 + num2
  }
  try {
    r2 = (num2.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 + num2
  }
  const m = Math.pow(10, Math.max(r1, r2))
  return Math.round(accMul(num1, m) + accMul(num2, m)) / m
}

/**
 * 两个浮点数相减
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accSub(num1, num2) {
  let r1, r2
  try {
    r1 = (num1.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 - num2
  }
  try {
    r2 = (num2.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 - num2
  }
  const m = Math.pow(10, Math.max(r1, r2))
  return Math.round(accMul(num1, m) - accMul(num2, m)) / m
}

/**
 * 两个浮点数相乘
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accMul(num1, num2) {
  let m = 0
  const s1 = num1.toString()
  const s2 = num2.toString()
  try {
    m += (s1.split('.')[1] || []).length
  } catch (e) {
    return num1 * num2
  }
  try {
    m += (s2.split('.')[1] || []).length
  } catch (e) {
    return num1 * num2
  }
  return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m)
}

/**
 * 两个浮点数相除
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accDiv(num1, num2) {
  let m = 0
  const s1 = num1.toString()
  const s2 = num2.toString()
  try {
    m += (s1.split('.')[1] || []).length
  } catch (e) {
    return num1 / num2
  }
  try {
    m += (s2.split('.')[1] || []).length
  } catch (e) {
    return num1 / num2
  }
  const n1 = accMul(num1, Math.pow(10, m))
  const n2 = accMul(num2, Math.pow(10, m))
  return Number(n1) / Number(n2)
}

// 格式化文件大小
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 判断文件类型
export function isDocumentFile(filename) {
  if (!filename) return false;
  const ext = getFileExtension(filename).toLowerCase();
  return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(ext);
}

// 判断图片文件
export function isImageFile(filename) {
  if (!filename) return false;
  const ext = getFileExtension(filename).toLowerCase();
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext);
}

// 判断图片文件
export function isPdfFile(filename) {
  if (!filename) return false;
  const ext = getFileExtension(filename).toLowerCase();
  return ext === 'pdf';
}

//判断是否为文本文件
export function isTextFile(filename) {
  if (!filename) return false;
  const ext = getFileExtension(filename).toLowerCase();
  return ['txt', 'csv', 'json', 'xml', 'html', 'css', 'js'].includes(ext);
}

//获取文件扩展名
export function getFileExtension(filename) {
  if (!filename) return '';
  return filename.split('.').pop() || '';
}

//是否可预览文件
export function canPreviewFile(filename) {
  return isImageFile(filename) || isPdfFile(filename) || isTextFile(filename);
}

// 从十六进制颜色值获取CSS类名
export function getColorClassFromHex(hexColor) {
  const colorMap = {
    '#4caf50': 'green',
    '#9c27b0': 'purple',
    '#795548': 'brown',
    '#00bcd4': 'cyan',
    '#ff9800': 'orange',
    '#e60012': 'red',
    '#2196f3': 'blue'
  };
  return colorMap[hexColor] || 'red';
}

// 判断文件类型
export function getFileType(filename) {
  const ext = getFileExtension(filename).toLowerCase();

  if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'json', 'xml', 'html'].includes(ext)) {
    return 'document';
  } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
    return 'image';
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) {
    return 'video';
  } else if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(ext)) {
    return 'audio';
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return 'archive';
  } else {
    return 'other';
  }
}

// 判断是否为允许的文件类型
export function isAllowedFileType(ext) {
  return allowedFileTypes.some(type => type.toLowerCase().includes(ext.toLowerCase()));
}

// 格式化日期时间
export function formatDateTime(date) {
  if (!date) return '';

  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');

  return `${year}/${month}/${day} ${hours}:${minutes}`;
}

// 格式化日期
export function formatDate(date) {
  if (!date) return '';

  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');

  return `${year}/${month}/${day}`;
}

export function  isEmail(val) {
    var pattern = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
    var domains= ["qq.com","163.com","vip.163.com","263.net","yeah.net","sohu.com","sina.cn","sina.com","eyou.com","gmail.com","hotmail.com","42du.cn"];
    if(pattern.test(val)) {
        var domain = val.substring(val.indexOf("@")+1);
        for(var i = 0; i< domains.length; i++) {
            if(domain == domains[i]) {
                return true;
            }
        }
    }
    return false;
}

// 获取时间分类
export function getTimeCategory(dateTime) {
  if (!dateTime) return '其他';

  const emailDate = new Date(dateTime);
  const now = new Date();
  
  // 设置时间为当天的开始（00:00:00）
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  
  // 计算本周开始时间（周一 00:00:00）
  const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
  const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // 周日算作距离周一6天
  const thisWeekStart = new Date(today.getTime() - daysFromMonday * 24 * 60 * 60 * 1000);
  
  // 计算本月开始时间（1号 00:00:00）
  const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  
  // 计算上个月开始时间
  const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  
  // 计算上周开始时间（上周一 00:00:00）
  const lastWeekStart = new Date(thisWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000);

  // 今天
  if (emailDate >= today) {
    return '今天';
  }

  // 昨天
  if (emailDate >= yesterday) {
    return '昨天';
  }

  // 本周（除今天和昨天外的本周其他天）
  if (emailDate >= thisWeekStart) {
    return '本周';
  }

  // 上周
  if (emailDate >= lastWeekStart) {
    return '上周';
  }

  // 本月（除本周和上周外的本月其他天）
  if (emailDate >= thisMonthStart) {
    return '本月';
  }

  // 上个月
  if (emailDate >= lastMonthStart) {
    return '上个月';
  }

  // 更早
  return '更早';
}

// 按时间分组邮件
export function groupEmailsByTime(emails) {
  if (!emails || !Array.isArray(emails)) {
    return [];
  }

  const groups = {};
  const categoryOrder = ['今天', '昨天', '本周', '上周', '本月', '上个月', '更早', '其他'];

  // 按时间分类邮件
  emails.forEach(email => {
    const emailTime = email.sentTime || email.receivedTime;
    const category = getTimeCategory(emailTime);

    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(email);
  });

  // 按预定义顺序返回分组
  const result = [];
  categoryOrder.forEach(category => {
    if (groups[category] && groups[category].length > 0) {
      result.push({
        category,
        count: groups[category].length,
        emails: groups[category]
      });
    }
  });

  return result;
}

// 格式化邮件时间显示
export function formatEmailTime(dateTime) {
  if (!dateTime) return '';

  const emailDate = new Date(dateTime);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

  // 今天：显示时间
  if (emailDate >= today) {
    const hours = String(emailDate.getHours()).padStart(2, '0');
    const minutes = String(emailDate.getMinutes()).padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  // 昨天：显示"昨天"
  if (emailDate >= yesterday) {
    return '昨天';
  }

  // 本年：显示月-日
  if (emailDate.getFullYear() === now.getFullYear()) {
    const month = String(emailDate.getMonth() + 1).padStart(2, '0');
    const day = String(emailDate.getDate()).padStart(2, '0');
    return `${month}-${day}`;
  }

  // 其他：显示年-月-日
  const year = emailDate.getFullYear();
  const month = String(emailDate.getMonth() + 1).padStart(2, '0');
  const day = String(emailDate.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
