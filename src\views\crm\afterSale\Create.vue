<template>
  <xr-create :loading="loading" :title="title" @close="close" @save="save">
    <create-sections v-loading="loading" title="基本信息" class="sections">
      <el-form ref="form" :model="fieldForm" :rules="fieldRules" :validate-on-rule-change="false" class="wk-form"
        label-position="top">
        <wk-form-items v-for="(children, index) in fieldList" :key="index" :field-from="fieldForm"
          :field-list="children" @change="formChange">
          <template slot-scope="{ data }">
            <!-- attachment 类型字段现在由 WkField 组件统一处理 -->
            <crm-relative-cell v-if="data && (data.formType == 'customer' || data.formType == 'product')" :value="fieldForm[data.field]"
              :disabled="data.disabled" :relative-type="data.formType" @value-change="otherChange($event, data)" />
          </template>
        </wk-form-items>
      </el-form>
    </create-sections>

    <create-sections title="异常照片" class="sections">
      <detail-img :file-list="abnormalImages" :limit="9" @change="abnormalImagesChange"
        @delete="abnormalImagesDelete" />
    </create-sections>

    <create-sections title="验证照片" class="sections">
      <detail-img :file-list="verifyImages" :limit="9" @change="verifyImagesChange" @delete="verifyImagesDelete" />
    </create-sections>
  </xr-create>
</template>

<script>
import { filedGetFieldAPI } from '@/api/crm/common'
import { crmSaleRepairSaveAPI, crmSaleRepairMouldNumberHistoryAPI, crmSaleRepairMouldNameListAPI } from '@/api/crm/saleRepair'
import { crmCustomerIndexAPI } from '@/api/crm/customer'
import XrCreate from '@/components/XrCreate'
import CreateSections from '@/components/CreateSections'
import WkFormItems from '@/components/NewCom/WkForm/WkFormItems'
import DetailImg from './components/DetailImg'
import { CrmRelativeCell } from '@/components/CreateCom'

import crmTypeModel from '@/views/crm/model/crmTypeModel'
import CustomFieldsMixin from '@/mixins/CustomFields'

export default {
  name: 'SaleRepairCreate',
  components: {
    XrCreate,
    CreateSections,
    WkFormItems,
    DetailImg,
    CrmRelativeCell
  },
  mixins: [CustomFieldsMixin],
  props: {
    action: {
      type: Object,
      default: () => {
        return {
          type: 'save',
          id: '',
          data: {}
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      ignoreFields: [],
      baseFields: [],
      fieldList: [],
      fieldForm: {},
      fieldRules: {},
      abnormalImages: [],
      verifyImages: []
    }
  },
  computed: {
    title() {
      return this.isEdit ? '编辑维修单' : '新建维修单'
    },
    isEdit() {
      return this.action.type === 'update'
    }
  },
  watch: {
    'action.editDetail': {
      handler(data) {
        if (data) {
          // 获取图片列表
          if (data.abnormalImages) {
            this.abnormalImages = data.abnormalImages.map(item => {
              return {
                ...item,
                url: item.url || item.filePath
              }
            })
          }
          if (data.verifyImages) {
            this.verifyImages = data.verifyImages.map(item => {
              return {
                ...item,
                url: item.url || item.filePath
              }
            })
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getField()
  },

  mounted() { },
  methods: {
    /**
     * 获取字段值，特殊处理productCode字段
     */
    getItemValueWithProductCodeFix(item, detail, type) {
      // 特殊处理productCode字段
      if (item.fieldName === 'productCode' && type === 'update') {
        if (item.value && typeof item.value === 'string') {
          // 将字符串值转换为数组格式，以匹配CrmRelativeCell组件的期望
          return [{
            num: item.value,
            name: item.value,
            productId: item.value,
            id: item.value
          }]
        } else if (Array.isArray(item.value)) {
          return item.value
        } else {
          return []
        }
      }
      
      // 其他字段使用默认处理
      return this.getItemValue(item, detail, type)
    },

    /**
     * 获取数据
     */
    otherChange(data, field) {
      if (field.formType === 'customer') {
        let contractForCount = 0
        for (let mainIndex = 0; mainIndex < this.fieldList.length; mainIndex++) {
          const children = this.fieldList[mainIndex]
          for (let index = 0; index < children.length; index++) {
            const element = children[index]
            // 需要处理 需关联客户信息或客户下信息
            const handleFields = [
              'businessId',
              'contactsId',
              'companyUserId'
            ]

            // 添加请求关联
            const addRelation = ['businessId', 'contactsId']

            // 需要disabled
            const addDisabled = ['businessId', 'contactsId']

            // 复制
            const getValueObj = {
              contactsId: data => {
                if (!data.contactsId) {
                  return []
                }
                return [
                  {
                    name: data.contactsName || '',
                    contactsId: data.contactsId
                  }
                ]
              },
              companyUserId: data => {
                if (!data.ownerUserId) {
                  return []
                }
                return [
                  {
                    realname: data.ownerUserName || '',
                    userId: data.ownerUserId
                  }
                ]
              }
            }

            if (handleFields.includes(element.field)) {
              if (data.value.length > 0) {
                element.disabled = false

                // 增加关联信息
                const customerItem = data.value[0]
                if (addRelation.includes(element.field)) {
                  customerItem['moduleType'] = 'customer'
                  element['relation'] = customerItem
                }

                // 填充值
                if (getValueObj[element.field]) {
                  this.fieldForm[element.field] = getValueObj[element.field](customerItem)
                } else {
                  this.fieldForm[element.field] = []
                }
              } else {
                // 禁用
                element.disabled = !!addDisabled.includes(element.field)

                if (addRelation.includes(element.field)) {
                  element['relation'] = {}
                }

                this.fieldForm[element.field] = []
              }

              contractForCount++
              if (contractForCount == handleFields.length) {
                break
              }
            }
          }
        }

        // 重置产品信息
        this.fieldForm.product = {
          product: [],
          totalPrice: '',
          discountRate: ''
        }
        this.fieldForm.money = ''
        // this.debouncedGetWkFlowList('money', this.fieldForm)
      } else if (field.formType === 'business') {
        if (data.value.length > 0) {
          this.getBusinessProduct(data.value[0].businessId).then(resData => {
            const businessData = resData || {}
            this.fieldForm.product = {
              product: businessData.list,
              totalPrice: businessData.money,
              discountRate: businessData.discountRate
            }
            this.fieldForm.money = businessData.money || ''
            // this.debouncedGetWkFlowList('money', this.fieldForm)
          }).catch(() => { })
        }
      } else if (field.formType === 'product') {
        this.fieldForm.money = data.value.totalPrice || ''
        
        // 自动填充产品名称
        if (data.value.length > 0) {
          const selectedProduct = data.value[0]
          this.autoFillProductName(selectedProduct)
        }
        
        // this.debouncedGetWkFlowList('money', this.fieldForm)
      } else if (field.formType === 'attachment') {
      }
      this.$set(this.fieldForm, field.field, data.value)
      this.$refs.crmForm.validateField(field.field)
    },
    getField() {
      this.loading = true
      const params = {
        label: crmTypeModel.afterSale
      }

      // 编辑模式时传入ID获取详情数据
      if (this.isEdit) {
        params.id = this.action.id
      }

      filedGetFieldAPI(params)
        .then(res => {
          const list = res.data || []

          const assistIds = this.getFormAssistIds(list)

          const baseFields = []
          const fieldList = []
          const fieldRules = {}
          const fieldForm = {}

          list.forEach(children => {
            const fields = []
            children.forEach(item => {
              const temp = this.getFormItemDefaultProperty(item)
              temp.show = !assistIds.includes(item.formAssistId)


              const canEdit = this.getItemIsCanEdit(item, this.action.type)
              // 是否能编辑权限
              if (temp.show && canEdit) {
                fieldRules[temp.field] = this.getRules(item)
              }

              // 是否可编辑
              temp.disabled = !canEdit

              // 特殊字段允许多选
              this.getItemRadio(item, temp)

              // 获取默认值 - 区分新建和编辑模式
              if (temp.show) {
                if (this.action.type != 'update' && item.fieldName === 'orderDate') {
                  fieldForm[temp.field] = this.$moment().format('YYYY-MM-DD')
                } else {
                  fieldForm[temp.field] = this.getItemValueWithProductCodeFix(item, this.action.data, this.action.type)
                }
              }
              fields.push(temp)
              baseFields.push(item)
            })
            fieldList.push(fields)
          })

          this.baseFields = baseFields
          this.fieldList = fieldList
          this.fieldForm = fieldForm
          this.fieldRules = fieldRules

          // 编辑模式时，处理图片数据
          if (this.isEdit && this.action.data) {
            this.handleEditModeData(this.action.data)
          }

          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 处理编辑模式的数据填充
     */
    handleEditModeData(editData) {
      // 处理异常照片
      if (editData.abnormalImages && Array.isArray(editData.abnormalImages)) {
        this.abnormalImages = editData.abnormalImages.map(item => ({
          ...item,
          url: item.url || item.filePath
        }))
      }

      // 处理验证照片
      if (editData.verifyImages && Array.isArray(editData.verifyImages)) {
        this.verifyImages = editData.verifyImages.map(item => ({
          ...item,
          url: item.url || item.filePath
        }))
      }
    },

    /**
     * 查询模具编号历史记录
     */
    queryMouldNumberHistory(keyword) {
      if (!keyword) return
      crmSaleRepairMouldNumberHistoryAPI({ keyword })
        .then(res => {
          const fieldList = this.fieldList.map(item => {
            if (item.field === 'mouldNumber') {
              item.options = res.data.map(number => {
                return {
                  label: number,
                  value: number
                }
              })
            }
            return item
          })
          this.fieldList = fieldList
        })
        .catch(() => { })
    },

    /**
     * 查询客户列表
     */
    queryCustomerList(keyword) {
      if (!keyword) return
      crmCustomerIndexAPI({ search: keyword, page: 1, limit: 10 })
        .then(res => {
          const fieldList = this.fieldList.map(item => {
            if (item.field === 'customerName') {
              item.options = (res.data.list || []).map(customer => {
                return {
                  label: customer.customerName,
                  value: customer.customerName
                }
              })
            }
            return item
          })
          this.fieldList = fieldList
        })
        .catch(() => { })
    },

    /**
     * 查询模具名称列表
     */
    queryMouldNameList(keyword) {
      if (!keyword) return
      crmSaleRepairMouldNameListAPI({ keyword })
        .then(res => {
          const fieldList = this.fieldList.map(item => {
            if (item.field === 'mouldName') {
              item.options = res.data.map(name => {
                return {
                  label: name,
                  value: name
                }
              })
            }
            return item
          })
          this.fieldList = fieldList
        })
        .catch(() => { })
    },

    /**
     * 验证唯一
     */
    UniquePromise({ field, value }) {
      return this.getUniquePromise(field, value, this.action)
    },

    /**
     * 表单字段变化
     */
    formChange(field, index, value, valueList) {
      if ([
        'select',
        'checkbox'
      ].includes(field.formType) &&
        field.remark === 'options_type' &&
        field.optionsData) {
        const { fieldForm, fieldRules } = this.getFormContentByOptionsChange(this.fieldList, this.fieldForm)
        this.fieldForm = fieldForm
        this.fieldRules = fieldRules
      }
    },

    /**
     * 自动填充产品名称
     */
    autoFillProductName(selectedProduct) {
      // 遍历表单字段，查找产品名称相关字段并自动填充
      for (let mainIndex = 0; mainIndex < this.fieldList.length; mainIndex++) {
        const children = this.fieldList[mainIndex]
        for (let index = 0; index < children.length; index++) {
          const element = children[index]
          
          // 可能的产品名称字段名
          const productNameFields = [
            'productName',
            'product_name', 
            'name',
            'mouldName',
            'mould_name'
          ]
          
          // 如果找到产品名称字段，自动填充
          if (productNameFields.includes(element.field)) {
            // 优先使用产品的name字段，如果没有则使用num字段
            const productName = selectedProduct.description || ''
            this.$set(this.fieldForm, element.field, productName)
          }
        }
      }
    },

    /**
     * 表单字段获取焦点
     */
    formFocus(field) {
      if (field === 'mouldNumber') {
        this.queryMouldNumberHistory('')
      } else if (field === 'customerName') {
        this.queryCustomerList('')
      } else if (field === 'mouldName') {
        this.queryMouldNameList('')
      }
    },

    /**
     * 异常照片变化
     */
    abnormalImagesChange(list) {
      this.abnormalImages = list
    },

    /**
     * 异常照片删除
     */
    abnormalImagesDelete(item, index) {
      this.abnormalImages.splice(index, 1)
    },

    /**
     * 验证照片变化
     */
    verifyImagesChange(list) {
      this.verifyImages = list
    },

    /**
     * 验证照片删除
     */
    verifyImagesDelete(item, index) {
      this.verifyImages.splice(index, 1)
    },

    /**
     * 关闭
     */
    close() {
      this.$emit('close')
    },

    /**
     * 保存
     */
    save() {
      this.loading = true
      const crmForm = this.$refs.form
      crmForm.validate(valid => {
        if (valid) {
          const params = this.getSubmiteParams([].concat.apply([], this.fieldList), this.fieldForm)
          // 图片信息
          params.entity.productCode = this.fieldForm.productCode[0].name;
          params.entity.abnormalImages = this.abnormalImages
          params.entity.verifyImages = this.verifyImages
          this.submiteParams(params)
        } else {
          this.loading = false
          // 提示第一个error
          this.getFormErrorMessage(crmForm)
          return false
        }
      })
    },

    /**
     * 提交上传
     */
    submiteParams(params) {
      if (this.isEdit) {
        params.entity.afterSaleId = this.action.id
        params.entity.batchId = this.action.batchId
      }

      // 相关添加时候的多余提交信息
      if (
        this.action.relativeData &&
        Object.keys(this.action.relativeData).length
      ) {
        params = { ...params, ...this.action.relativeData }
      }
      crmSaleRepairSaveAPI(params)
        .then(res => {
          this.loading = false

          this.$message.success(
            this.isEdit ? '编辑成功' : '添加成功'
          )

          this.close()

          // 保存成功
          this.$emit('save-success', {
            type: 'afterSale'
          })
        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
// 表单布局样式
::v-deep .wk-form-items {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;

  // textarea 字段独占一行，宽度为80%，左对齐
  .el-form-item.wk-form-item.is-textarea {
    width: 80% !important;
    flex: 0 0 80% !important;
    padding: 0 !important;
    margin-right: 0 !important;
    margin-left: 10px;

    // 确保textarea内容区域左对齐
    .el-form-item__content {
      text-align: left !important;
    }
  }

  // 其他字段（除 textarea 外）两个一行
  .el-form-item.wk-form-item:not(.is-textarea) {
    width: calc(50% - 6px) !important;
    flex: 0 0 calc(50% - 6px) !important;
    margin-right: 12px;

    &:nth-child(even) {
      margin-right: 0;
      margin-left: 0;
    }
  }

  // 确保字段间距和左对齐
  .el-form-item.wk-form-item {
    margin-bottom: 16px !important;

    .el-form-item__label {
      text-align: left !important;
      justify-content: flex-start !important;
    }

    .el-form-item__content {
      text-align: left !important;
    }
  }
}
</style>