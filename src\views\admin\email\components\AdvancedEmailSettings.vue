<template>
  <el-dialog
    :visible="advancedSettingsVisible"
    :append-to-body="true"
    :close-on-click-modal="false"
    width="800px"
    custom-class="no-padding-dialog"
    @close="closeClick">
    <span slot="title" class="el-dialog__title">添加邮箱账号</span>
    <div v-loading="loading" class="advanced-email-settings">
      <!-- 基本信息 -->
      <div class="section">
        <h3 class="section-title">基本信息</h3>
        <div class="form-grid">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">邮箱账户：</label>
              <el-input
                v-model="formData.emailAddress"
                :disabled="operateType == 'view' || operateType == 'edit'"
                placeholder="请输入内容"
                class="form-input" />
            </div>
            <div class="form-item">
              <label class="form-label">账户类型：</label>
              <el-select
                v-model="formData.type"
                :disabled="operateType == 'view' || operateType == 'edit'"
                placeholder="请选择账户类型"
                class="form-input">
                <el-option label="个人" :value=1 />
                <el-option label="公共" :value=2 />
              </el-select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">邮箱密码：</label>
              <el-input
                v-model="formData.emailPassword"
                :disabled="operateType == 'view'"
                type="password"
                placeholder="请输入密码"
                class="form-input" />
            </div>
            <div class="form-item">
              <label class="form-label">拥有人：</label>
              <wk-user-dialog-select
                v-model="formData.userId"
                radio
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">显示名字：</label>
              <el-input
                v-model="formData.displayName"
                :disabled="operateType == 'view'"
                placeholder="请输入内容"
                class="form-input" />
            </div>
            <div class="form-item">
              <label class="form-label">回复地址：</label>
              <el-input
                v-model="formData.replyEmailAddress"
                :disabled="operateType == 'view'"
                placeholder="请输入内容"
                class="form-input" />
            </div>
          </div>
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">邮箱协议：</label>
              <el-select
                v-model="formData.smtp"
                :disabled="operateType == 'view'  || operateType == 'edit'"
                placeholder="请选择协议"
                class="form-input">
                <el-option label="POP" value="pop" />
                <el-option label="IMAP" value="imap" />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <!-- 收信设置 -->
      <div class="section">
        <h3 class="section-title">收信设置</h3>
        <el-checkbox class="section-description" :disabled="operateType == 'view'" v-model="formData.disableReceiving">
          当前邮箱仅作为发件邮箱，无需收信
        </el-checkbox>
        
        <div class="form-item" v-if="!formData.disableReceiving">
          <label class="form-label">接收账户：</label>
          <el-input
            v-model="formData.receiveEmailAddress"
            :disabled="operateType == 'view'"
            placeholder="请输入内容"
            class="form-input-full" />
        </div>

        <div class="form-item" v-if="!formData.disableReceiving">
          <label class="form-label">保存邮件副本：</label>
          <el-radio-group v-model="formData.saveEmailCopies" :disabled="operateType == 'view'" class="radio-group">
            <el-radio label="1" :value=1>
              <span class="radio-dot forever"></span>
              永久保留
            </el-radio>
            <el-radio label="2" :value=2>保留10天</el-radio>
            <el-radio label="3" :value=3>保留30天</el-radio>
            <el-radio label="4" :value=4>保留60天</el-radio>
            <el-radio label="5" :value=5>不保留副本</el-radio>
          </el-radio-group>
        </div>

        <div class="form-item" v-if="!formData.disableReceiving">
          <label class="form-label">接收邮件范围：</label>
          <div class="checkbox-group">
            <el-checkbox v-model="formData.retrievalScope.imapSent" :disabled="operateType == 'view'">
              使用IMAP接收已发件箱邮件
            </el-checkbox>
            <el-checkbox v-model="formData.retrievalScope.imapCustom" :disabled="operateType == 'view'">
              使用IMAP接收自定义文件夹邮件
            </el-checkbox>
            <el-checkbox v-model="formData.retrievalScope.imapTrashPending" :disabled="operateType == 'view'">
              使用IMAP接收垃圾箱建档客户邮件(邮件保存于待处理)
            </el-checkbox>
            <el-checkbox v-model="formData.retrievalScope.imapTrashArchive" :disabled="operateType == 'view'">
              使用IMAP接收垃圾箱建档客户邮件(邮件保存于垃圾箱)
            </el-checkbox>
          </div>
        </div>

        <div class="form-item" v-if="!formData.disableReceiving">
          <label class="form-label">收件间隔</label>
          <div class="interval-control">
            <el-button
              size="mini"
              icon="el-icon-minus"
              :disabled="operateType == 'view'"
              @click="decreaseInterval" />
            <span class="interval-value">{{ formData.pollingInterval }}</span>
            <el-button
              size="mini"
              icon="el-icon-plus"
              :disabled="operateType == 'view'"
              @click="increaseInterval" />
            <span class="interval-unit">分钟</span>
          </div>
        </div>
      </div>

      <!-- 发信设置 -->
      <div class="section">
        <h3 class="section-title">发信设置</h3>
        
        <div class="form-item">
          <label class="form-label">发信认证：</label>
          <el-select
            v-model="formData.outgoingAuthType"
            :disabled="operateType == 'view'"
            placeholder="发信账户密码与收件相同（默认）"
            class="form-input-full">
            <el-option label="发信账户密码与收件相同（默认）" :value=1 />
            <el-option label="指定发信账户" :value=2 />
          </el-select>
        </div>

        <div class="form-row" v-if="formData.outgoingAuthType == 2">
          <div class="form-item">
            <label class="form-label">发送账号：</label>
            <div class="server-input-group">
              <el-input
                v-model="formData.sendEmailAddress"
                :disabled="operateType == 'view'"
                placeholder="请输入内容"
                class="server-input" />
              <span class="port-label">密码：</span>
              <el-input
                v-model="formData.sendEmailPassword"
                type="password"
                :disabled="operateType == 'view'"
                placeholder="请输入内容"
                class="port-input" />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label class="form-label">收信服务器：</label>
            <div class="server-input-group">
              <el-input
                v-model="formData.imapServer"
                disabled
                placeholder="请输入内容"
                class="server-input" />
              <el-checkbox v-model="formData.imapSsl" disabled class="ssl-checkbox">SSL</el-checkbox>
              <span class="port-label">端口：</span>
              <el-input
                v-model="formData.imapPort"
                disabled
                placeholder="请输入内容"
                class="port-input" />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <label class="form-label">SMTP服务器：</label>
            <div class="server-input-group">
              <el-input
                v-model="formData.smtpServer"
                disabled
                placeholder="请输入内容"
                class="server-input" />
              <el-checkbox v-model="formData.smtpSsl" disabled class="ssl-checkbox">SSL</el-checkbox>
              <span class="port-label">端口：</span>
              <el-input
                v-model="formData.smtpPort"
                placeholder="请输入内容"
                disabled
                class="port-input" />
            </div>
          </div>
        </div>

        <div class="form-item">
          <el-checkbox v-model="formData.encryption" :disabled="operateType == 'view'" disabled>
            如果服务器支持就使用STARTTLS加密传输
          </el-checkbox>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <div class="footer-content">
        <el-button
          type="primary"
          class="save-btn"
          @click="handleSave">保存</el-button>
        <el-button
          type="text"
          class="back-link"
          v-if="operateType == ''"
          @click="handleBackToQuick">返回到快速添加</el-button>
      </div>
      <div class="help-section">
        <span class="help-title">帮助</span>
        <el-button
          type="text"
          class="help-link"
          @click="handleHelpClick">常见邮箱的绑定方法</el-button>
      </div>
    </span>
  </el-dialog>
</template>

<script>
import { userListAPI } from '@/api/common'
import WkUserDialogSelect from '@/components/NewCom/WkUserDialogSelect'
import { addMailAccountAPI,updateMailAccountAPI } from '@/api/crm/email'
export default {
  name: 'AdvancedEmailSettings',
  components:{
    WkUserDialogSelect
  },
  props: {
    advancedSettingsVisible: {
      type: Boolean,
      default: false
    },
    initialData: {
      type: Object,
      default: () => ({})
    },
    operateType:{
      type:String,
      default: ''
    },
    from:{
      type:String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      userList: [], // 移到根级别
      formData: {
        emailAddress: '',
        emailPassword: '',
        displayName: '',
        protocol: '',
        userId:'',
        type: '',
        realname: '',
        replyEmailAddress: '',
        receiveEmailAddress: '',
        saveEmailCopies: null,
        retrievalScope: {
          imapSent: false,
          imapCustom: false,
          imapTrashPending: false,
          imapTrashArchive: false
        },
        sendEmailAddress:'',
        sendEmailPassword:'',
        pollingInterval: 3,
        outgoingAuthType: 1,
        imapServer: '',
        imapSsl: false,
        imapPort: '',
        smtpServer: '',
        smtpSsl: false,
        smtpPort: '',
        encryption: false,
        disableReceiving: false  //是否仅作为发件邮箱
      }
    }
  },
  watch: {
    advancedSettingsVisible(value) {
      if (value) {
        this.initFormData()
      }
    }
  },
  mounted() {
    if(this.from){
      this.formData.userId = this.$store.state.user.userInfo.userId;
    }
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      const retrievalScopeMap = {
        1: 'imapSent',
        2: 'imapCustom',
        3: 'imapTrashPending',
        4: 'imapTrashArchive'
      };
      const fields = [
        'emailAddress',
        'emailPassword',
        'type',  // 账户类型
        'userId',  // 拥有人ID
        'userName',  // 拥有人姓名
        'replyEmailAddress', //回复地址
        'displayName',
        'smtp',
        'receiveEmailAddress',
        'pollingInterval',
        'retrievalScope',
        'saveEmailCopies',
        'outgoingAuthType',
        'sendEmailAddress',
        'sendEmailPassword',
        'encryption',
        'smtpServer',
        'imapPort',
        'smtpSsl',
        'imapServer',
        'smtpPort',
        'imapSsl',
        'disableReceiving',
        'id'
      ];
      if(this.from){
        console.log("从邮件页面返回的数据", this.initialData);
        this.formData.realname = this.initialData.name;
      }else{
        fields.forEach(key => {
        const value = this.initialData[key];
        if (value != null) {
          if (key === 'retrievalScope') {
            const scopeObj = {
              imapSent: false,
              imapCustom: false,
              imapTrashPending: false,
              imapTrashArchive: false
            };

            // 兼容字符串 '2,3,4' 或数组 [2,3,4]
            const scopeArr = typeof value === 'string'
              ? value.split(',').map(Number)
              : Array.isArray(value) ? value : [];

            scopeArr.forEach(code => {
              const field = retrievalScopeMap[code];
              if (field) scopeObj[field] = true;
            });

            this.formData[key] = scopeObj;
          } else {
            this.formData[key] = value;
          }
        }
      });

      // 如果有userId和userName，确保用户在选项列表中
      if (this.initialData.userId && this.initialData.userName) {
        const existingUser = this.userList.find(user => user.userId === this.initialData.userId);
        if (!existingUser) {
          this.userList.unshift({
            userId: this.initialData.userId,
            realname: this.initialData.userName
          });
        }
      }

      }
    },

    /**
     * 减少收件间隔
     */
    decreaseInterval() {
      if (this.formData.pollingInterval > 1) {
        this.formData.pollingInterval--
      }
    },
    handleSelectFocus() {
    if (this.userList.length === 0) {
      this.loadInitialUserList();
    }
  },
  
  // 加载初始用户列表
  loadInitialUserList() {
    userListAPI({
      pageType: 0,
      realname: '' // 空字符串获取默认列表
    }).then(res => {
      this.userList = res.data.list || [];
    }).catch(() => {
      console.error('加载用户列表失败');
    });
  },
    handleRemoteSearch(query) {
        // 通知父组件进行搜索
        userListAPI({
          pageType: 0,
          realname: query
        }).then(res => {
          this.userList = res.data.list || [];
        }).catch(() => {
        })
    },
    increaseInterval() {
      this.formData.pollingInterval++
    },

    /**
     * 保存设置
     */
    handleSave() {
      if (!this.formData.emailAddress) {
        this.$message.error('请输入邮箱账户')
        return
      }
      if (!this.formData.emailPassword) {
        this.$message.error('请输入邮箱密码')
        return
      }

      let retrievalScopeTemp = '';
      if (this.formData.retrievalScope.imapSent) {
        retrievalScopeTemp += '1,';
      }
      if (this.formData.retrievalScope.imapCustom) {
        retrievalScopeTemp += '2,';
      }
      if (this.formData.retrievalScope.imapTrashPending) {
        retrievalScopeTemp += '3,';
      }
      if (this.formData.retrievalScope.imapTrashArchive) {
        retrievalScopeTemp += '4,';
      }
      retrievalScopeTemp = retrievalScopeTemp.slice(0, -1);

      this.loading = true
      let params = {
        emailAddress: this.formData.emailAddress,
        emailPassword: this.formData.emailPassword,
        type:this.formData.type,     // 账户类型
        userId:this.formData.userId,   // 拥有人
        replyEmailAddress: this.formData.replyEmailAddress ? this.formData.replyEmailAddress :this.formData.emailAddress , //回复地址
        displayName:this.formData.displayName,  // 显示名字
        smtp:this.formData.smtp,   //邮箱协议
        receiveEmailAddress:this.formData.receiveEmailAddress ? this.formData.replyEmailAddress :this.formData.receiveEmailAddress, //接收地址
        pollingInterval:this.formData.pollingInterval, //接收间隔
        retrievalScope:retrievalScopeTemp, //接收邮件范围（1:已发件箱，2:自定义文件夹，3:垃圾箱建档客户，4:垃圾箱非建档客户）
        saveEmailCopies:this.formData.saveEmailCopies,//保存副本策略
        outgoingAuthType:this.formData.outgoingAuthType, //发信认证方式
        sendEmailAddress:this.formData.sendEmailAddress,
        sendEmailPassword:this.formData.sendEmailPassword,
        encryption:this.formData.encryption,  //是否使用加密传输
        smtpServer:this.formData.smtpServer,
        smtpPort:this.formData.smtpPort,
        smtpSsl:this.formData.smtpSsl ? 1 : 0,
        imapServer:this.formData.imapServer,  //收信服务器地址
        imapPort:this.formData.imapPort,     //收信服务器端口
        imapSsl:this.formData.imapSsl ? 1 : 0,
        disableReceiving:this.formData.disableReceiving ? 1 : 0
      }
      if(this.operateType == 'edit'){
        params.id = this.formData.id;
      }
      this.loading = true
      if(this.operateType == 'edit'){
        updateMailAccountAPI(params)
        .then(res => {
          this.loading = false
          setTimeout(() => {
            this.loading = false
            this.$emit('refresh-list')
            this.$emit('success', this.formData)
            this.closeClick()
          }, 1000)
        })
        .catch(() => {
          this.loading = false
        })     
      }else{
        addMailAccountAPI(params)
        .then(res => {
          this.loading = false
          setTimeout(() => {
            this.loading = false
            this.$emit('refresh-list')
            this.$emit('success', this.formData)
            this.closeClick()
          }, 1000)
        })
        .catch(() => {
          this.loading = false
        })
      }  
    },

    /**
     * 返回到快速添加
     */
    handleBackToQuick() {
      this.$emit('back-to-quick')
      this.closeClick()
    },

    /**
     * 帮助链接点击
     */
    handleHelpClick() {
      this.$message.info('正在跳转到帮助页面...')
    },

    /**
     * 关闭弹框
     */
    closeClick() {
      this.$emit('update:advancedSettingsVisible', false)
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-email-settings {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 8px;
  }

  .section-description {
    font-size: 14px;
    color: #718096;
    margin-bottom: 15px;
  }
}

.form-grid {
  .form-row {
    display: flex;
    gap: 20px;
    .form-item {
      flex: 1;
    }
  }
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .form-label {
    width: 100px;
    flex-shrink: 0;
    text-align: right;
    margin-right: 15px;
    font-size: 14px;
    color: #2d3748;
  }

  .form-input {
    flex: 1;
  }

  .form-input-full {
    width: 300px;
  }
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  ::v-deep .el-radio {
    margin-right: 0;
    
    .el-radio__label {
      font-size: 14px;
      color: #2d3748;
    }
  }

  .radio-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;

    &.forever {
      background-color: #0052CC;
    }
  }
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;

  ::v-deep .el-checkbox {
    .el-checkbox__label {
      font-size: 14px;
      color: #2d3748;
    }
  }

  .predict-option {
    ::v-deep .el-checkbox__label {
      color: #3182ce;
    }
  }
}

.interval-control {
  display: flex;
  align-items: center;
  gap: 10px;

  .interval-value {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    min-width: 20px;
    text-align: center;
  }

  .interval-unit {
    font-size: 14px;
    color: #718096;
  }
}

.server-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;

  .server-input {
    flex: 1;
  }

  .ssl-checkbox {
    ::v-deep .el-checkbox__label {
      font-size: 14px;
      color: #2d3748;
    }
  }

  .port-label {
    font-size: 14px;
    color: #718096;
  }

  .port-input {
    width: 100px;
  }
}

.dialog-footer {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .footer-content {
    display: flex;
    align-items: center;
    gap: 20px;

    .save-btn {
      background-color: #0052CC;
      border-color: #0052CC;
      color: white;
      padding: 10px 30px;

      &:hover {
        background-color: #c53030;
        border-color: #c53030;
      }
    }

    .back-link {
      color: #0052CC;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #c53030;
      }
    }
  }

  .help-section {
    display: flex;
    align-items: center;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;

    .help-title {
      font-size: 14px;
      color: #718096;
    }

    .help-link {
      color: #3182ce;
      font-size: 14px;
      padding: 0;

      &:hover {
        color: #2c5aa0;
      }
    }
  }
}
</style>
