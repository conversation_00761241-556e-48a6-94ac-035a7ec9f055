{"name": "5kcrm", "version": "12.0.0", "description": "", "author": "5kcrm <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --fix --ext .js,.vue src"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^1.0.2", "@fullcalendar/core": "^4.3.1", "@fullcalendar/daygrid": "^4.3.0", "@fullcalendar/interaction": "^4.3.0", "@fullcalendar/list": "^4.3.0", "@fullcalendar/timegrid": "^4.3.0", "@fullcalendar/timeline": "^4.3.0", "@fullcalendar/vue": "^4.3.1", "@tinymce/tinymce-vue": "^3.2.0", "axios": "0.18.0", "clipboard": "^2.0.4", "core-js": "^3.6.5", "echarts": "^5.2.0", "el-bigdata-table": "git+https://gitee.com/devi001/el-bigdata-table.git", "element-ui": "git+https://gitee.com/devi001/wk-element.git", "file-saver": "^2.0.1", "id-validator": "^1.3.0", "js-cookie": "2.2.1", "jsencrypt": "3.2.1", "lockr": "^0.8.5", "lucide-vue": "^0.511.0", "marked": "^4.0.8", "mathjs": "^10.0.2", "normalize.css": "8.0.1", "nprogress": "0.2.0", "number-precision": "^1.5.0", "numeral": "^2.0.6", "nzh": "^1.0.4", "path-to-regexp": "2.4.0", "pinyin-match": "1.0.9", "print-js": "^1.6.0", "qrcodejs2": "0.0.2", "regenerator-runtime": "^0.13.9", "sass": "^1.26.8", "sass-loader": "^8.0.2", "sass-resources-loader": "^2.2.5", "signature_pad": "3.0.0-beta.4", "throttle-debounce": "^2.1.0", "v-calendar": "2.3.0", "v-viewer": "^1.6.4", "vue": "^2.6.14", "vue-bus": "^1.1.0", "vue-calendar-component": "^2.8.2", "vue-cropper": "^0.4.8", "vue-html2pdf": "^1.8.0", "vue-i18n": "^8.7.0", "vue-moment": "^4.1.0", "vue-radial-progress": "^0.3.2", "vue-router": "3.0.1", "vue-virtual-scroller": "^1.0.10", "vue2-animate": "^2.1.2", "vue2-org-tree": "1.3.1", "vuedraggable": "2.24.3", "vuex": "3.0.1", "xlsx": "^0.14.1", "xss": "^1.0.6"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "^8.2.6", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "less": "^4.1.2", "less-loader": "^7.3.0", "postcss": "^8.4.8", "postcss-html": "^1.3.0", "postcss-scss": "^4.0.3", "runjs": "4.3.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "stylelint": "^14.5.3", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^5.0.0", "stylelint-scss": "^4.1.0", "stylelint-webpack-plugin": "^2.4.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}