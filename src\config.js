const getLocationOrigin = () => {
  return window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '')
}
const macUrl = '#'
const winUrl = '#'
const iOSUrl = '#'
const androidUrl = '#'

const registerUrl = '#'
const loginUrl = '#'
const contactUrl = '#' // 联系我们

const companyName = '奔达 - CRM'
const version = 'V12.0.0'
const baiduKey = 'LEcDcElRR6zFXoaG6jtANQYW'

const build = 20220110

// 默认表格样式
const tableStyle = {
  stripe: true, // 斑马纹
  class: [] // 'is-right-border-style', 'is-bottom-border-style'
}

const languages = [
  { code: 'zh', name: '中文' },
  { code: 'en', name: '英文' },
  { code: 'ja', name: '日语' },
  { code: 'ko', name: '韩语' },
  { code: 'fr', name: '法语' },
  { code: 'de', name: '德语' },
  { code: 'es', name: '西班牙语' },
  { code: 'ru', name: '俄语' },
   { code: 'pt', name: '葡萄牙语' },       // 巴西、葡萄牙
  { code: 'hi', name: '印地语' },          // 印度广泛使用
  { code: 'ar', name: '阿拉伯语' },        // 中东、北非地区广泛使用
  { code: 'it', name: '意大利语' },        // 欧洲常用
  { code: 'vi', name: '越南语' },          // 东南亚
  { code: 'th', name: '泰语' },            // 东南亚
  { code: 'id', name: '印尼语' },          // 东南亚人口大国
  { code: 'tr', name: '土耳其语' },        // 欧洲和西亚交汇地带
  { code: 'pl', name: '波兰语' },          // 东欧
  { code: 'uk', name: '乌克兰语' },        // 东欧
  { code: 'ms', name: '马来语' },          // 马来西亚、新加坡部分人群使用
  { code: 'fa', name: '波斯语' },          // 伊朗等国
  { code: 'he', name: '希伯来语' }         // 以色列官方语言
]

const countries = [
  {
    "region": "亚洲",
    "countries": [
      "中国", "日本", "韩国", "朝鲜", "蒙古", "印度", "巴基斯坦", "孟加拉国", "尼泊尔", "不丹",
      "斯里兰卡", "马尔代夫", "阿富汗", "伊朗", "伊拉克", "叙利亚", "黎巴嫩", "以色列",
      "巴勒斯坦", "约旦", "沙特阿拉伯", "阿联酋", "卡塔尔", "科威特", "巴林", "阿曼", "也门",
      "土耳其", "格鲁吉亚", "亚美尼亚", "阿塞拜疆", "哈萨克斯坦", "乌兹别克斯坦", "土库曼斯坦",
      "塔吉克斯坦", "吉尔吉斯斯坦", "马来西亚", "新加坡", "印度尼西亚", "文莱", "菲律宾",
      "泰国", "越南", "缅甸", "老挝", "柬埔寨", "东帝汶"
    ]
  },
  {
    "region": "欧洲",
    "countries": [
      "英国", "法国", "德国", "意大利", "西班牙", "葡萄牙", "比利时", "荷兰", "卢森堡", "爱尔兰",
      "挪威", "瑞典", "芬兰", "丹麦", "冰岛", "奥地利", "瑞士", "波兰", "捷克", "斯洛伐克", "匈牙利",
      "罗马尼亚", "保加利亚", "克罗地亚", "斯洛文尼亚", "塞尔维亚", "黑山", "北马其顿", "阿尔巴尼亚",
      "希腊", "爱沙尼亚", "拉脱维亚", "立陶宛", "乌克兰", "白俄罗斯", "俄罗斯", "摩尔多瓦", "马耳他", "安道尔", "摩纳哥", "圣马力诺", "梵蒂冈", "波黑", "科索沃", "列支敦士登"
    ]
  },
  {
    "region": "非洲",
    "countries": [
      "埃及", "利比亚", "突尼斯", "阿尔及利亚", "摩洛哥", "毛里塔尼亚", "马里", "尼日尔", "乍得",
      "苏丹", "南苏丹", "厄立特里亚", "吉布提", "索马里", "埃塞俄比亚", "肯尼亚", "乌干达", "卢旺达",
      "布隆迪", "坦桑尼亚", "刚果（金）", "刚果（布）", "加蓬", "赤道几内亚", "中非", "喀麦隆", "尼日利亚",
      "贝宁", "多哥", "加纳", "布基纳法索", "象牙海岸", "利比里亚", "塞拉利昂", "几内亚", "几内亚比绍",
      "冈比亚", "塞内加尔", "佛得角", "赞比亚", "安哥拉", "津巴布韦", "莫桑比克", "纳米比亚", "博茨瓦纳",
      "南非", "莱索托", "斯威士兰", "马达加斯加", "科摩罗", "毛里求斯", "塞舌尔"
    ]
  },
  {
    "region": "北美洲",
    "countries": [
      "美国", "加拿大", "墨西哥", "危地马拉", "伯利兹", "洪都拉斯", "萨尔瓦多", "尼加拉瓜", "哥斯达黎加",
      "巴拿马", "巴哈马", "古巴", "牙买加", "海地", "多米尼加", "圣基茨和尼维斯", "安提瓜和巴布达",
      "多米尼克", "圣卢西亚", "圣文森特和格林纳丁斯", "格林纳达", "特立尼达和多巴哥"
    ]
  },
  {
    "region": "南美洲",
    "countries": [
      "巴西", "阿根廷", "智利", "乌拉圭", "巴拉圭", "玻利维亚", "秘鲁", "厄瓜多尔", "哥伦比亚",
      "委内瑞拉", "圭亚那", "苏里南"
    ]
  },
  {
    "region": "大洋洲",
    "countries": [
      "澳大利亚", "新西兰", "巴布亚新几内亚", "斐济", "所罗门群岛", "瓦努阿图", "萨摩亚", "汤加",
      "密克罗尼西亚", "马绍尔群岛", "帕劳", "基里巴斯", "瑙鲁", "图瓦卢"
    ]
  }
]

export default {
  version,
  build,
  companyName,
  getLocationOrigin,
  baiduKey,
  macUrl,
  winUrl,
  iOSUrl,
  androidUrl,
  registerUrl,
  tableStyle,
  loginUrl,
  contactUrl,
  languages,
  countries
}
