<template>
  <slide-view
    v-empty="!canShowDetail"
    :listener-ids="listenerIDs"
    :no-listener-ids="noListenerIDs"
    :no-listener-class="noListenerClass"
    :body-style="{padding: 0, height: '100%'}"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限"
    @afterEnter="viewAfterEnter"
    @close="hideView">
    <div
      ref="crmDetailMain"
      v-loading="loading"
      class="detail-main no-padding">
      <flexbox
        v-if="canShowDetail && detailData"
        direction="column"
        align="stretch"
        class="d-container">
        <c-r-m-detail-head
          :id="id"
          :class="{'is-shadow': bodyIsScroll}"
          :detail="detailData"
          :crm-type="crmType"
          :page-list="pageList"
          :tag-info="tagInfo"
          @pageChange="pageChange"
          @handle="detailHeadHandle"
          @close="hideView" />

        <div class="d-container-body" @scroll="bodyScroll">
          <detail-head-base :list="headDetails" />
          <relative-stage-records
            v-if="statusShow"
            :id="id"
            :crm-type="crmType"
            :detail="detailData"
            @handle="detailHeadHandle"
          />
          <flexbox
            class="left-right-wrap"
            :class="{'is-hidden-right': !detailData.examineRecordId }"
            align="stretch">
            <div class="left">
              <el-tabs
                v-model="tabCurrentName"
                nav-mode="more">
                <el-tab-pane
                  v-for="(item, index) in tabNames"
                  :key="index"
                  :label="item.label"
                  :name="item.name"
                  lazy>
                  <template slot="label">
                    <el-badge
                      :value="item.num"
                      :hidden="item.num <= 0"
                      type="undefined">
                      {{ item.label }}
                    </el-badge>
                  </template>
                  <component
                    :is="item.name"
                    :id="id"
                    :ref="item.name"
                    :detail="detailData"
                    :crm-type="crmType"
                    @handle="detailHeadHandle" />
                </el-tab-pane>
              </el-tabs>
            </div>
            <div class="right">
              <examine-info-section
                v-if="detailData.examineRecordId"
                :id="id"
                :record-id="detailData.examineRecordId"
                :owner-user-id="detailData.ownerUserId"
                examine-type="crm_receivables"
                @on-handle="examineHandle" />
            </div>
          </flexbox>
        </div>
      </flexbox>
    </div>

    <c-r-m-all-create
      v-if="isCreate"
      :action="{type: 'update', id: id, batchId: detailData.batchId}"
      :crm-type="crmType"
      @save-success="editSaveSuccess"
      @close="isCreate=false" />
  </slide-view>
</template>

<script>
import { crmReceivablesReadAPI } from '@/api/crm/receivables'

import SlideView from '@/components/SlideView'
import CRMDetailHead from '../components/CRMDetailHead'
import RelativeStageRecords from '../components/RelativeStageRecords' // 阶段记录
import CRMEditBaseInfo from '../components/CRMEditBaseInfo' // 基本信息
import RelativeFiles from '../components/RelativeFiles' // 相关附件
import RelativeHandle from '../components/RelativeHandle' // 相关操作
import RelativePrint from '../components/RelativePrint' // 相关打印
import RelativeTeam from '../components/RelativeTeam' // 团队成员

import CRMAllCreate from '../components/CRMAllCreate' // 新建页面
import ExamineInfoSection from '@/components/Examine/ExamineInfoSection'

import DetailMixin from '../mixins/Detail'
import { separator } from '@/filters/vueNumeralFilter/filters'

export default {
  // 客户管理 的 回款详情
  name: 'ReceivablesDetail',
  components: {
    SlideView,
    CRMDetailHead,
    RelativeStageRecords,
    CRMEditBaseInfo,
    RelativeFiles,
    RelativeHandle,
    RelativePrint,
    RelativeTeam,
    ExamineInfoSection,
    CRMAllCreate
  },
  mixins: [DetailMixin],
  props: {
    // 详情信息id
    id: [String, Number],
    // 监听的dom 进行隐藏详情
    listenerIDs: {
      type: Array,
      default: () => {
        return ['crm-main-container']
      }
    },
    // 不监听
    noListenerIDs: {
      type: Array,
      default: () => {
        return []
      }
    },
    noListenerClass: {
      type: Array,
      default: () => {
        return ['el-table__body']
      }
    }
  },
  data() {
    return {
      // 展示加载loading
      loading: false,
      crmType: 'receivables',
      // 名称
      name: '',
      headDetails: [],
      tabCurrentName: 'CRMEditBaseInfo',
      // 编辑操作
      isCreate: false
    }
  },
  computed: {
    tabNames() {
      var tempsTabs = [{ label: '详细资料', name: 'CRMEditBaseInfo' }]

      tempsTabs.push({ label: '团队成员', num: this.tabsNumber.memberCount, name: 'RelativeTeam' })
      tempsTabs.push({
        label: '附件', num: this.tabsNumber.fileCount,
        name: 'RelativeFiles'
      })

      if (this.crm.receivables && this.crm.receivables.print) {
        tempsTabs.push({ label: '打印记录', name: 'RelativePrint' })
      }

      tempsTabs.push({ label: '操作记录', name: 'RelativeHandle' })
      // if (this.statusShow) {
      //   tempsTabs.unshift({ label: '阶段记录', name: 'RelativeStageRecords' })
      // }
      return tempsTabs
    }
  },
  mounted() {},
  methods: {
    /**
     * 详情
     */
    getDetial() {
      this.loading = true
      crmReceivablesReadAPI({
        id: this.id
      })
        .then(res => {
          this.loading = false
          const resData = res.data || {}
          this.name = resData.number
          this.detailData = resData

          this.headDetails = [
            { title: '客户名称', value: resData.customerName },
            { title: '合同金额', value: separator(resData.contractMoney || 0) },
            // { title: '合同名称', value: resData.contractName },
            { title: '回款日期', value: resData.returnTime },
            { title: '回款金额', value: separator(resData.money || 0) },
            { title: '负责人', value: resData.ownerUserName }
          ]
        })
        .catch(() => {
          this.loading = false
          this.hideView()
        })
    },

    /**
     * 关闭
     */
    hideView() {
      this.$emit('hide-view')
    },

    // /**
    //  * 编辑成功
    //  */
    // editSaveSuccess() {
    //   this.$emit('handle', { type: 'save-success' })
    //   this.getDetial()
    // },

    /**
     * 审核操作
     */
    examineHandle() {
      this.detailHeadHandle({ type: 'examine' })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/crmdetail.scss";
</style>
