<template>
  <div class="app-main">
    <slot name="header" />
    <keep-alive :include="cachedViews">
      <router-view class="app-main-content" :key="`${$route.name}_${$route.params.id}_${$route.params.mode}`" />
    </keep-alive>
    <slot />
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'AppMain',
  computed: {
    ...mapState('tabsView', ['cachedViews'])
  }
}
</script>

<style scoped>
.app-main {
  position: relative;
  height: 100%;
}
</style>
