<template>
  <div class="product-form">
    <div class="product-header">
      <flexbox justify="space-between" align="center">
        <div class="header-title">产品明细</div>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="addProduct">添加产品</el-button>
      </flexbox>
    </div>

    <div v-if="productList.length > 0" class="product-table">
      <el-table
        :data="productList"
        border
        style="width: 100%;">
        <el-table-column
          prop="productName"
          label="产品名称"
          min-width="150">
          <template slot-scope="scope">
            <crm-relative-cell
              v-model="scope.row.productId"
              :relation="{ moduleType: 'product', params: {} }"
              relative-type="product"
              @value-change="(data) => productChange(data, scope.$index)" />
          </template>
        </el-table-column>

        <el-table-column
          prop="categoryName"
          label="产品类型"
          width="120">
          <template slot-scope="scope">
            {{ scope.row.categoryName || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="unit"
          label="单位"
          width="80">
          <template slot-scope="scope">
            {{ scope.row.unit || '-' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="price"
          label="单价"
          width="120">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.price"
              :precision="2"
              :min="0"
              :max="999999999"
              size="small"
              style="width: 100%;"
              @change="calculateAmount(scope.$index)" />
          </template>
        </el-table-column>

        <el-table-column
          prop="num"
          label="数量"
          width="100">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.num"
              :precision="2"
              :min="0"
              :max="999999999"
              size="small"
              style="width: 100%;"
              @change="calculateAmount(scope.$index)" />
          </template>
        </el-table-column>

        <el-table-column
          prop="discount"
          label="折扣(%)"
          width="100">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.discount"
              :precision="2"
              :min="0"
              :max="100"
              size="small"
              style="width: 100%;"
              @change="calculateAmount(scope.$index)" />
          </template>
        </el-table-column>

        <el-table-column
          prop="subtotal"
          label="小计"
          width="120">
          <template slot-scope="scope">
            ¥{{ (scope.row.subtotal || 0).toLocaleString() }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="80"
          fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-delete"
              @click="removeProduct(scope.$index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="product-summary">
        <flexbox justify="end" class="summary-row">
          <div class="summary-label">总计：</div>
          <div class="summary-value">¥{{ totalAmount.toLocaleString() }}</div>
        </flexbox>
      </div>
    </div>

    <div v-else class="product-empty">
      <wk-empty
        :props="{
          buttonTitle: '添加产品',
          showButton: true
        }"
        @click="addProduct" />
    </div>

    <!-- 产品选择弹窗 -->
    <product-selector
      :visible.sync="productSelectorVisible"
      @confirm="handleProductSelect" />
  </div>
</template>

<script>
import CrmRelativeCell from '@/components/CreateCom/CrmRelativeCell'
import ProductSelector from './ProductSelector'
import WkEmpty from '@/components/WkEmpty'

export default {
  name: 'QuotationProductForm',
  components: {
    CrmRelativeCell,
    ProductSelector,
    WkEmpty
  },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      productList: [],
      productSelectorVisible: false
    }
  },
  computed: {
    totalAmount() {
      return this.productList.reduce((total, item) => {
        return total + (item.subtotal || 0)
      }, 0)
    }
  },
  watch: {
    value: {
      handler(val) {
        this.productList = val || []
      },
      immediate: true,
      deep: true
    },
    productList: {
      handler(val) {
        this.$emit('input', val)
        this.$emit('total-change', this.totalAmount)
      },
      deep: true
    }
  },
  methods: {
    /**
     * 添加产品
     */
    addProduct() {
      this.productSelectorVisible = true
    },

    /**
     * 产品选择确认
     */
    handleProductSelect(products) {
      products.forEach(product => {
        const existIndex = this.productList.findIndex(item => item.productId === product.productId)

        if (existIndex === -1) {
          // 新增产品
          this.productList.push({
            productId: product.productId,
            productName: product.productName,
            categoryName: product.categoryName,
            unit: product.unit,
            price: product.price || 0,
            num: 1,
            discount: 100,
            subtotal: product.price || 0
          })
        } else {
          // 已存在，增加数量
          this.productList[existIndex].num += 1
          this.calculateAmount(existIndex)
        }
      })
    },

    /**
     * 产品变更
     */
    productChange(data, index) {
      if (data && data.data) {
        const product = data.data
        this.$set(this.productList, index, {
          ...this.productList[index],
          productId: product.productId,
          productName: product.productName,
          categoryName: product.categoryName,
          unit: product.unit,
          price: product.price || 0
        })
        this.calculateAmount(index)
      }
    },

    /**
     * 移除产品
     */
    removeProduct(index) {
      this.productList.splice(index, 1)
    },

    /**
     * 计算金额
     */
    calculateAmount(index) {
      const item = this.productList[index]
      if (item) {
        const price = item.price || 0
        const num = item.num || 0
        const discount = item.discount || 100

        item.subtotal = (price * num * discount / 100)
        this.$set(this.productList, index, item)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.product-form {
  .product-header {
    margin-bottom: 16px;

    .header-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
  }

  .product-table {
    .el-table {
      margin-bottom: 16px;
    }
  }

  .product-summary {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e6e6e6;
    border-top: none;

    .summary-row {
      .summary-label {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-right: 12px;
      }

      .summary-value {
        font-size: 16px;
        font-weight: 600;
        color: #e74c3c;
      }
    }
  }

  .product-empty {
    padding: 40px 0;
    text-align: center;
  }
}
</style>
