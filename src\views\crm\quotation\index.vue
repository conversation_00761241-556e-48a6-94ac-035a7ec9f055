<template>
  <div>
    <wk-page-header
      :title="config.showModuleName ? '报价管理' : ''"
      :help="getHelpObj(crmType, 'index')"
      :dropdowns="getDefaultHeaderHandes()"
      @command="pageHeaderCommand">
      <template slot="right">
        <el-button
          v-if="saveAuth"
          type="primary"
          @click="createClick">新建报价</el-button>
      </template>
    </wk-page-header>

    <div
      v-empty="!indexAuth"
      xs-empty-icon="nopermission"
      xs-empty-text="暂无权限"
      class="crm-container">
      <wk-table-header
        :search.sync="search"
        :tabs="sceneList"
        :active-tab.sync="sceneId"
        :selection-list="tableSelectionList"
        :operations="handleOperations"
        :condition-type-fun="undefined"
        :fields="getFilterFields"
        :props="tableHeaderProps.props"
        :filter-header-props="tableHeaderProps.filterHeaderProps"
        :filter-form-props="tableHeaderProps.filterFormProps"
        :scene-set-props="tableHeaderProps.sceneSetProps"
        :scene-create-props="tableHeaderProps.sceneCreateProps"
        @tabs-change="sceneSelect"
        @operations-click="tableOperationsClick"
        @event-change="tableHeaderHandle"
        @filter-change="handleFilter"
      />
      <el-table
        id="crm-table"
        v-loading="loading"
        :row-height="rowHeight"
        :data="list"
        :height="tableHeight"
        :cell-class-name="cellClassName"
        :row-key="`${crmType}Id`"
        :class="crmTableClass"
        :stripe="tableStyleObj.stripe"
        use-virtual
        highlight-current-row
        style="width: 100%;"
        @row-click="handleRowClick"
        @sort-change="sortChange"
        @header-dragend="handleHeaderDragend"
        @selection-change="handleSelectionChange">
        <el-table-column
          show-overflow-tooltip
          reserve-selection
          type="selection"
          fixed
          align="center"
          width="55" />
        <el-table-column
          v-for="(item, index) in fieldList"
          :key="index"
          :fixed="item.isLock === 1"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :class-name="item.width>60 ? 'column' : '' "
          sortable="custom"
          show-overflow-tooltip>
          <template v-if="item.width>60" slot="otherHeader" slot-scope="scope">
            <el-button
              :icon="item.isLock === 1 ? 'wk wk-unlock' : 'wk wk-lock'"
              class="el-lock-btn"
              type="text"
              @click.stop="fieldFixed(item)" />
            <el-button
              v-if="showFilter(item)"
              class="el-filter-btn"
              type="text"
              icon="wk wk-screening"
              @click.stop="showFilterClick(item)" />
          </template>
          <template slot-scope="{ row, column }">
            <wk-field-view
              :props="item"
              :form-type="item.formType"
              :value="row[column.property]">
              <template>
                {{ fieldFormatter(row, column, row[column.property], item) }}
              </template>
            </wk-field-view>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          width="40">
          <template slot-scope="scope">
            <el-dropdown
              trigger="click"
              @command="handleCommand($event, scope)">
              <flexbox
                class="table-dropdown-btn"
                justify="center"
                align="center">
                <i class="el-icon-more" />
              </flexbox>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, index) in getRowHandles(scope.row)"
                  :key="index"
                  :icon="item.icon"
                  :command="item.command">{{ item.name }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <el-table-column />

        <wk-empty
          slot="empty"
          :props="{
            buttonTitle: '新建报价',
            showButton: saveAuth
          }"
          @click="createClick"
        />
        <field-set
          slot="other"
          :crm-type="crmType"
          @change="setSave" />
      </el-table>
      <div class="p-contianer">
        <el-dropdown trigger="click" placement="top">
          <el-button class="dropdown-btn"><i class="el-icon-s-fold" /></el-button>
          <el-dropdown-menu slot="dropdown" class="wk-table-style-dropdown-menu">
            <el-dropdown-item>
              <span @click.stop><el-switch v-model="tableStyleObj.rightBorderShow" />显示竖向分割线</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click.stop><el-switch v-model="tableStyleObj.bottomBorderShow" />显示横向分割线</span>
            </el-dropdown-item>
            <el-dropdown-item>
              <span @click.stop><el-switch v-model="tableStyleObj.stripe" />显示斑马纹</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          :total="total"
          :pager-count="5"
          class="p-bar"
          layout="prev, pager, next, sizes, total, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
        <span v-if="quotationPageData" class="money-bar">报价金额：{{ quotationPageData.quotationSumMoney || 0 | separator }}</span>
      </div>
    </div>

    <!-- 报价详情 -->
    <quotation-detail
      v-if="showDview"
      :id="rowID"
      :crm-type="rowType"
      :page-list="crmType == rowType ? list : []"
      :page-index.sync="rowIndex"
      class="d-view"
      @handle="detailHandle"
      @hide-view="showDview=false" />

    <!-- 新建编辑 -->
    <quotation-create
      v-if="isCreate"
      :action="createAction"
      @save-success="editSaveSuccess"
      @hiden-view="isCreate=false" />

    <!-- 转移 -->
    <transfer-handle
      v-if="transferDialogShow"
      :props="transferHandleProps"
      :dialog-visible.sync="transferDialogShow"
      @handle="handleHandle" />

    <!-- 删除提醒 -->
    <el-dialog
      v-if="deleteDialogShow"
      :visible.sync="deleteDialogShow"
      :close-on-click-modal="false"
      title="删除确认"
      width="400px">
      <p>确定要删除选中的报价单吗？删除后将无法恢复。</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="confirmDelete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import QuotationDetail from './Detail'
import QuotationCreate from './Create'
import TransferHandle from '@/components/Page/SelectionHandle/TransferHandle'

import TableMixin from '../mixins/Table'
import { crmQuotationIndexAPI, crmQuotationDeleteAPI, crmQuotationTransferAPI } from '@/api/crm/quotation'
import { floatAdd } from '@/utils'

export default {
  /** 报价管理 */
  name: 'QuotationIndex',
  components: {
    QuotationDetail,
    QuotationCreate,
    TransferHandle
  },
  mixins: [TableMixin],
  data() {
    return {
      crmType: 'quotation',
      createType: 'quotation',
      createAction: {
        type: 'save',
        id: '',
        data: {}
      },
      isCreate: false,
      createShow: false,
      moneyData: null, // 列表金额
      // 转移
      transferDialogShow: false,
      transferHandleProps: {},
      // 删除
      deleteDialogShow: false,
      deleteIds: []
    }
  },
  computed: {
    // 表格操作按钮
    handleOperations() {
      return this.getOperations([
        'transfer',
        'export',
        'delete'
      ])
    },

    // 报价金额数据
    quotationPageData() {
      if (!this.moneyData || JSON.stringify(this.moneyData) == '{}') {
        return null
      }

      if (this.selectionList.length == 0) {
        return this.moneyData || {}
      } else {
        let money = 0.0
        for (let index = 0; index < this.selectionList.length; index++) {
          const element = this.selectionList[index]
          money = floatAdd(money, parseFloat(element.totalAmount || 0))
        }
        return {
          quotationSumMoney: money.toFixed(2)
        }
      }
    }
  },
  methods: {
    /**
     * 新建点击
     */
    createClick() {
      this.createAction = { type: 'save' }
      this.isCreate = true
    },
    /**
     * 通过回调控制class
     */
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'quotationName') {
        return 'can-visit--underline can-visit--bold'
      } else if (column.property === 'customerName') {
        return 'can-visit--underline'
      } else {
        return ''
      }
    },

    /**
     * 编辑成功
     */
    editSaveSuccess() {
      this.getList()
    },

    /**
     * 获取行操作按钮
     */
    getRowHandles(row) {
      const handles = []

      if (this.$auth('crm.quotation.read')) {
        handles.push({
          name: '详情',
          command: 'detail',
          icon: 'wk wk-detail'
        })
      }

      if (this.$auth('crm.quotation.update') && row.status !== 3) { // 非归档状态可编辑
        handles.push({
          name: '编辑',
          command: 'edit',
          icon: 'wk wk-edit'
        })
      }

      if (this.$auth('crm.quotation.copy')) {
        handles.push({
          name: '复制',
          command: 'copy',
          icon: 'wk wk-copy'
        })
      }

      if (this.$auth('crm.quotation.download')) {
        handles.push({
          name: '下载',
          command: 'download',
          icon: 'wk wk-download'
        })
      }

      if (this.$auth('crm.quotation.print')) {
        handles.push({
          name: '打印',
          command: 'print',
          icon: 'wk wk-print'
        })
      }

      if (this.$auth('crm.quotation.send') && row.status === 0) { // 草稿状态可发送
        handles.push({
          name: '发送',
          command: 'send',
          icon: 'wk wk-send'
        })
      }

      if (this.$auth('crm.quotation.archive') && row.status !== 3) { // 非归档状态可归档
        handles.push({
          name: '归档',
          command: 'archive',
          icon: 'wk wk-archive'
        })
      }

      if (this.$auth('crm.quotation.delete')) {
        handles.push({
          name: '删除',
          command: 'delete',
          icon: 'wk wk-delete'
        })
      }

      return handles
    },

    /**
     * 行操作按钮点击
     */
    handleCommand(command, scope) {
      const row = scope.row
      const id = row[`${this.crmType}Id`]

      switch (command) {
        case 'detail':
          this.rowID = id
          this.rowType = this.crmType
          this.showDview = true
          break
        case 'edit':
          this.createAction = {
            type: 'update',
            id: id,
            data: row
          }
          this.isCreate = true
          break
        case 'copy':
          this.handleCopy(id)
          break
        case 'download':
          this.handleDownload(id)
          break
        case 'print':
          this.handlePrint(id)
          break
        case 'send':
          this.handleSend(id)
          break
        case 'archive':
          this.handleArchive(id)
          break
        case 'delete':
          this.deleteIds = [id]
          this.deleteDialogShow = true
          break
      }
    },

    /**
     * 表格操作按钮点击
     */
    tableOperationsClick(type) {
      if (type === 'delete') {
        this.deleteIds = this.selectionList.map(item => item[`${this.crmType}Id`])
        this.deleteDialogShow = true
      } else if (type === 'transfer') {
        this.transferHandleProps = {
          request: crmQuotationTransferAPI,
          params: { ids: this.selectionList.map(item => item[`${this.crmType}Id`]) },
          showChangeOwner: false,
          showRemoveType: false
        }
        this.transferDialogShow = true
      }
    },

    /**
     * 确认删除
     */
    confirmDelete() {
      this.loading = true
      crmQuotationDeleteAPI({ ids: this.deleteIds })
        .then(() => {
          this.$message.success('删除成功')
          this.deleteDialogShow = false
          this.getList()
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 复制报价单
     */
    handleCopy(id) {
      this.createAction = {
        type: 'copy',
        id: id
      }
      this.isCreate = true
    },

    /**
     * 下载报价单
     */
    handleDownload(id) {
      this.$message.info('下载功能开发中...')
      // TODO: 实现下载功能
    },

    /**
     * 打印报价单
     */
    handlePrint(id) {
      this.$message.info('打印功能开发中...')
      // TODO: 实现打印功能
    },

    /**
     * 发送报价单
     */
    handleSend(id) {
      this.$message.info('发送功能开发中...')
      // TODO: 实现发送功能
    },

    /**
     * 归档报价单
     */
    handleArchive(id) {
      this.$message.info('归档功能开发中...')
      // TODO: 实现归档功能
    },

    /**
     * 字段格式化
     */
    fieldFormatter(row, column) {
      // 状态格式化
      if (column.property === 'status') {
        const statusMap = {
          0: '草稿',
          1: '已发送',
          2: '已确认',
          3: '已归档',
          4: '已失效'
        }
        return statusMap[row[column.property]] || '未知'
      }

      // 金额格式化
      if (column.property === 'totalAmount') {
        return row[column.property] ? `¥${Number(row[column.property]).toLocaleString()}` : '-'
      }

      // 日期格式化
      if (column.property === 'validDate' || column.property === 'createTime') {
        return row[column.property] ? this.$moment(row[column.property]).format('YYYY-MM-DD') : '-'
      }

      return row[column.property] || '-'
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/table.scss";

.table-dropdown-btn {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  cursor: pointer;

  &:hover {
    background-color: #f2f6fc;
  }

  .el-icon-more {
    font-size: 14px;
    color: #666;
  }
}
</style>
