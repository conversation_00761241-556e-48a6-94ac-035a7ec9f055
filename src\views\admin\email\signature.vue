<template>
  <div class="signature-management">
    <!-- 页面标题 -->
    <div class="page-title">
      <h2>签名管理</h2>
    </div>

    <!-- 顶部区域 -->
    <div class="signature-header">
      <el-card class="header-card">
        <div class="header-content">
          <div class="user-section">
            <el-select
              v-model="selectedUser"
              placeholder="请选择用户"
              class="user-select"
              :loading="loading"
              @change="handleUserChange">
              <el-option
                v-for="user in userList"
                :key="user.userId"
                :label="user.userName"
                :value="user.userId" />
            </el-select>
          </div>
          <div class="default-signature-section">
            <el-checkbox
              v-model="enableDefaultSignature"
              @change="handleDefaultSignatureChange">
              启用默认签名复选框/转发邮件场景中
            </el-checkbox>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 邮箱账户展示区 -->
    <div class="email-account-section">
      <el-card class="account-card">
        <div class="account-content">
          <span class="account-label">本人全部邮箱</span>
          <div class="email-address">
            {{ currentEmailAccount }}
          </div>
        </div>
      </el-card>
    </div>

    <!-- 签名配置区 -->
    <div class="signature-config-section">
      <el-card class="config-card">
        <div class="config-header">
          <div class="default-signature-select">
            <label class="section-label">默认签名：</label>
            <el-select
              v-model="selectedDefaultSignature"
              placeholder="请选择默认签名"
              class="signature-select"
              @change="handleDefaultSignatureSelect">
              <el-option
                v-for="signature in signatureList"
                :key="signature.id"
                :label="signature.name"
                :value="signature.id" />
            </el-select>
          </div>
        </div>
        
        <div class="editor-section">
          <label class="section-label editor-label">签名内容：</label>
          <div class="editor-container">
            <tinymce
              ref="signatureEditor"
              v-model="signatureContent"
              :height="300"
              :init="editorInit"
              @input="handleContentChange" />
            <div class="editor-footer">
              <span class="byte-count">{{ signatureByteCount }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 底部操作区 -->
    <div class="signature-actions">
      <el-card class="actions-card">
        <div class="actions-content">
          <el-button
            type="primary"
            :loading="saveLoading"
            @click="handleSaveSignature">
            保存签名
          </el-button>
          <el-button @click="handleResetSignature">
            重置
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import { queryMailAccountPageListAPI } from '@/api/crm/email'

export default {
  name: 'SignatureManagement',
  components: {
    Tinymce
  },
  data() {
    return {
      // 用户相关
      selectedUser: '',
      userList: [],
      loading: false,
      
      // 默认签名设置
      enableDefaultSignature: true,
      selectedDefaultSignature: '',
      
      // 邮箱账户
      currentEmailAccount: '',
      
      // 签名列表
      signatureList: [
        { id: '1', name: '商务签名' },
        { id: '2', name: '技术支持签名' },
        { id: '3', name: '市场推广签名' }
      ],
      
      // 签名内容
      signatureContent: '',
      signatureByteCount: 0,
      
      // 保存状态
      saveLoading: false,
      
      // 富文本编辑器配置
      editorInit: {
        height: 300,
        menubar: false,
        statusbar: false,
        placeholder: '请输入签名内容...',
        content_style: 'body { font-family: Arial, sans-serif; font-size: 14px; }',
        toolbar: 'undo redo | bold italic underline strikethrough | fontselect fontsizeselect | forecolor backcolor | alignleft aligncenter alignright | bullist numlist | link image | removeformat',
        plugins: 'link image lists textcolor colorpicker',
        branding: false,
        elementpath: false,
        resize: false
      }
    }
  },
  mounted() {
    this.initializeData()
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo
    },
  },
  methods: {
    /**
     * 初始化数据
     */
    initializeData() {
      // 加载用户列表
      this.loadUserList()
      // 加载签名列表
      this.loadSignatureList()
      // 设置默认用户
      if (this.userList.length > 0) {
        this.selectedUser = this.userList[0].userId
      }
    },
    
    /**
     * 加载用户列表
     */
    async loadUserList() {
      this.loading = true
      try {
        const params = {
          page: 1,
          limit: 1000 // 获取所有邮箱账户
        }
        const response = await queryMailAccountPageListAPI(params)
        if (response.data && response.data.records) {
          this.userList = response.data.records;
          const matchedAccount = this.userList.find(item => item.userId === this.userInfo.userId);
            if(matchedAccount){
              this.selectedUser = matchedAccount.userId;
              this.currentEmailAccount = matchedAccount.emailAddress;
            }
        }
      } catch (error) {
        console.error('加载邮箱账户列表失败:', error)
        this.$message.error('加载邮箱账户列表失败')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载签名列表
     */
    async loadSignatureList() {
      try {
        // 这里应该调用实际的API
        // const response = await getSignatureListAPI()
        // this.signatureList = response.data
        console.log('加载签名列表')
      } catch (error) {
        this.$message.error('加载签名列表失败')
      }
    },
    
    /**
     * 用户选择变化
     */
    handleUserChange(userId) {
      console.log('选择用户:', userId)
      // 更新当前邮箱账户
      const selectedUser = this.userList.find(user => user.userId === userId)
      if (selectedUser) {
        this.currentEmailAccount = selectedUser.emailAddress
      }
      // 根据用户加载对应的签名配置
      this.loadUserSignatureConfig(userId)
    },
    
    /**
     * 加载用户签名配置
     */
    async loadUserSignatureConfig(userId) {
      try {
        // 这里应该调用实际的API
        // const response = await getUserSignatureConfigAPI(userId)
        // this.signatureContent = response.data.content
        // this.selectedDefaultSignature = response.data.defaultSignatureId
        console.log('加载用户签名配置:', userId)
      } catch (error) {
        this.$message.error('加载用户签名配置失败')
      }
    },
    
    /**
     * 默认签名启用状态变化
     */
    handleDefaultSignatureChange(enabled) {
      console.log('默认签名启用状态:', enabled)
    },
    
    /**
     * 默认签名选择变化
     */
    handleDefaultSignatureSelect(signatureId) {
      console.log('选择默认签名:', signatureId)
      // 加载选中的签名内容到编辑器
      this.loadSignatureContent(signatureId)
    },
    
    /**
     * 加载签名内容
     */
    async loadSignatureContent(signatureId) {
      try {
        // 这里应该调用实际的API
        // const response = await getSignatureContentAPI(signatureId)
        // this.signatureContent = response.data.content
        console.log('加载签名内容:', signatureId)
      } catch (error) {
        this.$message.error('加载签名内容失败')
      }
    },
    
    /**
     * 签名内容变化
     */
    handleContentChange(content) {
      this.signatureContent = content
      // 计算字节数
      this.calculateByteCount(content)
    },
    
    /**
     * 计算字节数
     */
    calculateByteCount(content) {
      // 移除HTML标签，只计算纯文本字节数
      const textContent = content.replace(/<[^>]*>/g, '')
      // 使用UTF-8编码计算字节数
      this.signatureByteCount = new Blob([textContent]).size
    },
    
    /**
     * 保存签名
     */
    async handleSaveSignature() {
      // 验证签名内容
      if (!this.validateSignatureContent()) {
        return
      }
      
      this.saveLoading = true
      try {
        const signatureData = {
          userId: this.selectedUser,
          content: this.signatureContent,
          defaultSignatureId: this.selectedDefaultSignature,
          enableDefault: this.enableDefaultSignature
        }
        
        // 这里应该调用实际的API
        // await saveSignatureAPI(signatureData)
        console.log('保存签名数据:', signatureData)
        
        this.$message.success('签名保存成功')
      } catch (error) {
        this.$message.error('签名保存失败')
      } finally {
        this.saveLoading = false
      }
    },
    
    /**
     * 验证签名内容
     */
    validateSignatureContent() {
      // 检查签名内容是否为空
      const textContent = this.signatureContent.replace(/<[^>]*>/g, '').trim()
      if (!textContent) {
        this.$message.warning('签名内容不能为空')
        return false
      }
      
      // 检查签名内容是否超长（假设限制为1000字节）
      if (this.signatureByteCount > 1000) {
        this.$message.warning('签名内容过长，请控制在1000字节以内')
        return false
      }
      
      return true
    },
    
    /**
     * 重置签名
     */
    handleResetSignature() {
      this.$confirm('确定要重置签名内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.signatureContent = ''
        this.selectedDefaultSignature = ''
        this.signatureByteCount = 0
        this.$message.success('重置成功')
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.signature-management {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
  
  .page-title {
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .signature-header {
    margin-bottom: 20px;
    
    .header-card {
      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
        
        .user-section {
          display: flex;
          align-items: center;
          gap: 10px;
          
          .user-select {
            width: 200px;
          }
        }
        
        .default-signature-section {
          display: flex;
          align-items: center;
        }
      }
    }
  }
  
  .email-account-section {
    margin-bottom: 20px;
    
    .account-card {
      .account-content {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .account-label {
          font-weight: bold;
          color: #606266;
          background-color: #f0f0f0;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          white-space: nowrap;
        }
        
        .email-address {
          font-size: 14px;
          color: #303133;
          padding: 8px 12px;
          background-color: #f9f9f9;
          border-radius: 4px;
          border: 1px solid #e4e7ed;
          flex: 1;
        }
      }
    }
  }
  
  .signature-config-section {
    margin-bottom: 20px;
    
    .config-card {
      .config-header {
        margin-bottom: 20px;
        
        .default-signature-select {
          display: flex;
          align-items: center;
          gap: 10px;
          
          .signature-select {
            width: 200px;
          }
        }
      }
      
      .editor-section {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        
        .editor-label {
          white-space: nowrap;
          margin-top: 8px;
        }
        
        .editor-container {
          position: relative;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          flex: 1;
          
          .editor-footer {
            position: absolute;
            bottom: 10px;
            right: 15px;
            z-index: 1000;
            
            .byte-count {
              font-size: 12px;
              color: #909399;
              background-color: rgba(255, 255, 255, 0.9);
              padding: 2px 6px;
              border-radius: 3px;
              border: 1px solid #e4e7ed;
            }
          }
        }
      }
    }
  }
  
  .signature-actions {
    .actions-card {
      .actions-content {
        display: flex;
        gap: 10px;
        justify-content: flex-start;
      }
    }
  }
  
  .section-label {
    font-weight: 500;
    color: #606266;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .signature-management {
    padding: 10px;
    
    .header-content {
      flex-direction: column;
      align-items: flex-start !important;
    }
    
    .user-select,
    .signature-select {
      width: 100% !important;
    }
    
    .account-content {
      flex-direction: column !important;
      align-items: flex-start !important;
      
      .email-address {
        width: 100%;
      }
    }
    
    .editor-section {
      flex-direction: column !important;
      
      .editor-label {
        margin-top: 0 !important;
      }
    }
  }
}
</style>