.item-show {
  text-align: left;
}

.el-form {
  box-sizing: border-box;
  padding: 32px 40px;
  color: rgb(94, 108, 132);
  background: white;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 0 10px;
}

::v-deep .el-form-item {
  .el-input__inner {
    height: 40px;
    line-height: 40px;
    border-width: 2px;

    &::placeholder {
      font-size: 14px;
      font-weight: 500;
      color: $--color-text-placeholder;
    }

    &:hover {
      background-color: $--color-n30;
    }

    &:focus {
      background-color: #fff;
    }
  }
}

.handle-bar {
  margin-top: 28px;

  .el-button {
    width: 100%;
    padding: 9px 12px;
    font-size: 16px;
  }
}

.form-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: rgb(94, 108, 132);
}

.other-login {
  margin-top: 16px;

  &::before {
    display: block;
    margin-bottom: 16px;
    font-size: 11px;
    line-height: 1;
    color: $--color-n80;
    text-align: center;
    text-transform: uppercase;
    content: "或";
  }

  button {
    position: relative;
    box-sizing: border-box;
    display: inline-flex;
    align-items: baseline;
    justify-content: center;
    width: 100%;
    max-width: 100%;
    height: 40px !important;
    padding: 0 10px;
    font-size: inherit;
    font-style: normal;
    font-weight: bold;
    line-height: 40px !important;
    color: $--color-text-regular !important;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    background: #fff !important;
    border-width: 0;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.2) 1px 1px 5px 0 !important;
    transition: background 0.1s ease-out 0s, box-shadow 0.15s cubic-bezier(0.47, 0.03, 0.49, 1.38) 0s;
    -webkit-box-align: baseline;
    -webkit-box-pack: center;
  }
}

.other-handle {
  padding-top: 16px;
  margin-top: 32px;
  line-height: 20px;
  text-align: center;
  border-top: 1px solid rgb(213, 216, 222);

  .el-button {
    font-size: 14px;
  }

  span {
    padding: 0 8px;
  }
}

// 密码展示隐藏
.wk-icon-eye-solid {
  margin-right: 2px;
  font-size: 20px;
  cursor: pointer;
  user-select: none;
}
