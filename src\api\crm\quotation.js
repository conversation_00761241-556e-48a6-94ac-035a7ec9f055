import request from '@/utils/request'

/**
 * 新建编辑报价
 * @param {*} data
 */
export function crmQuotationSaveAPI(data) {
  const url = data.entity && data.entity.quotationId ? 'update' : 'add'
  return request({
    url: 'crmQuotation/' + url,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 报价列表
 * @param {*} data
 */
export function crmQuotationIndexAPI(data) {
  return request({
    url: 'crmQuotation/queryPageList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除报价
 * @param {*} data
 */
export function crmQuotationDeleteAPI(data) {
  return request({
    url: 'crmQuotation/deleteByIds',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 报价详情
 * @param {*} data
 */
export function crmQuotationReadAPI(data) {
  return request({
    url: `crmQuotation/queryById/${data.quotationId}`,
    method: 'post'
  })
}

/**
 * 报价状态更新
 * @param {*} data
 */
export function crmQuotationStatusAPI(data) {
  return request({
    url: 'crmQuotation/updateStatus',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取报价模板列表
 * @param {*} data
 */
export function crmQuotationTemplateListAPI(data) {
  return request({
    url: 'crmQuotationTemplate/queryList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取报价模板详情
 * @param {*} data
 */
export function crmQuotationTemplateDetailAPI(data) {
  return request({
    url: `crmQuotationTemplate/queryById/${data.templateId}`,
    method: 'post'
  })
}

/**
 * 报价单下载
 * @param {*} data
 */
export function crmQuotationDownloadAPI(data) {
  return request({
    url: 'crmQuotation/download',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 报价单打印
 * @param {*} data
 */
export function crmQuotationPrintAPI(data) {
  return request({
    url: 'crmQuotation/print',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 报价单归档
 * @param {*} data
 */
export function crmQuotationArchiveAPI(data) {
  return request({
    url: 'crmQuotation/archive',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 复制报价单
 * @param {*} data
 */
export function crmQuotationCopyAPI(data) {
  return request({
    url: 'crmQuotation/copy',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 发送报价单
 * @param {*} data
 */
export function crmQuotationSendAPI(data) {
  return request({
    url: 'crmQuotation/send',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取报价统计信息
 * @param {*} data
 */
export function crmQuotationStatisticsAPI(data) {
  return request({
    url: 'crmQuotation/statistics',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 报价转移
 * @param {*} data
 */
export function crmQuotationTransferAPI(data) {
  return request({
    url: 'crmQuotation/transfer',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
