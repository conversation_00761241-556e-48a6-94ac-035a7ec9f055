#calendar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

#calendar ::v-deep .fc-license-message {
  display: none !important;
}

#calendar ::v-deep .fc-toolbar {
  padding-top: 1em;

  .fc-left {
    padding-left: 20px;

    .fc-button-group {
      .fc-button {
        text-shadow: none;
      }

      .fc-state-default {
        padding: 0 1.2em;
        margin-right: 3px;
        background: #f0f0f0;
        border: 0;
      }

      .fc-state-down {
        text-shadow: none;
        box-shadow: none;
      }

      .fc-state-active {
        color: #fff;
        background: #2362fb;
      }
    }

    .fc-today-button {
      margin-right: 50px;
      background: #fff;
    }
  }

  .fc-center {
    margin-left: -277px;

    h2 {
      margin-top: 5px;
      font-size: 20px;
      font-weight: normal;
    }

    .fc-prevYear-button,
    .fc-prev-button,
    .fc-next-button,
    .fc-nextYear-button {
      color: $--color-text-secondary;
      background: none;
      border: 0;
      outline: none;
      box-shadow: none;
    }

    .fc-button-group {
      .fc-prev-button .fc-icon-left-single-arrow::after,
      .fc-next-button .fc-icon-right-single-arrow::after {
        font-size: 160%;
        font-weight: none;
      }
    }
  }
}

#calendar ::v-deep .fc-view-container {
  .fc-body {
    .fc-row {
      .fc-day {
        border-color: #e9e9e9;
      }

      .fc-bg {
        .fc-sat,
        .fc-sun {
          background: #fbfbfb;
        }

        .fc-today {
          background: none;
        }
      }

      .fc-content-skeleton {
        .fc-today {
          .fc-day-number {
            min-width: 16px;
            padding: 3px;
            color: #fff;
            text-align: center;
            background: #2362fb;
            border-radius: 50%;
          }
        }

        .fc-day-grid-event {
          margin-right: 5px;
          margin-left: 5px;
          border: 0 !important;
          border-radius: 23px;

          .fc-content {
            color: #fff;

            .fc-time {
              display: none;
            }

            .fc-title {
              float: left;
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }

      .fc-day-number {
        margin: 5px;
      }

      .fc-day-grid-event {
        padding: 2px 15px;
        margin-right: 0;
        margin-left: 0;
        border-left: 2px solid #ff9668 !important;
        border-radius: 0;
      }
    }
  }

  .fc-body > tr > .fc-widget-content {
    border: 0;
  }

  .fc-head {
    .fc-head-container {
      border: 0;
    }
  }
}

#calendar ::v-deep .fc-day-header {
  font-weight: normal;
  background: #f5f5f5;
  border-width: 0;

  span {
    height: 50px;
    line-height: 50px;
  }
}
