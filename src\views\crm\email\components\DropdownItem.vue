<template>
  <div
    class="dropdown-item"
    @click="handleClick"
    :class="{ 'danger': isDanger, 'disabled': disabled }"
  >
    <slot name="icon"></slot>
    <span class="dropdown-item-text"><slot></slot></span>
  </div>
</template>

<script>
export default {
  name: 'DropdownItem',
  props: {
    isDanger: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick(event) {
      if (this.disabled) {
        event.preventDefault();
        return;
      }
      this.$emit('click', event);
    }
  }
}
</script>

<style scoped>
.dropdown-item {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f5f7fa;
}

/* .dropdown-item.danger {
  color: #e60012;
} */

.dropdown-item.danger:hover {
  background-color: #fff1f0;
}

.dropdown-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown-item.disabled:hover {
  background-color: transparent;
}

.dropdown-item-text {
  margin-left: 8px;
}

/* 确保图标正确显示 */
.dropdown-item ::v-deep svg {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}
</style>
