<template>
  <div class="dropdown-container" v-click-outside="closeDropdown" :class="{ 'active': isOpen }">
    <button
      class="dropdown-trigger"
      @click="toggleDropdown"
      :class="{ 'active': isOpen }"
    >
      <slot name="trigger"></slot>
      <!-- <chevron-down-icon v-if="showChevron" class="icon-tiny" :class="{ 'rotate': isOpen }" /> -->
    </button>
    <div class="dropdown-menu" v-show="isOpen">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { ChevronDown } from 'lucide-vue'

export default {
  name: 'DropdownMenu',
  components: {
    ChevronDownIcon: ChevronDown,
  },
  props: {
    showChevron: {
      type: Boolean,
      default: true
    },
    placement: {
      type: String,
      default: 'bottom-right',
      validator: (value) => ['bottom-left', 'bottom-right', 'top-left', 'top-right'].includes(value)
    }
  },
  data() {
    return {
      isOpen: false
    }
  },
  methods: {
    toggleDropdown() {
      this.isOpen = !this.isOpen;
    },
    closeDropdown() {
      this.isOpen = false;
    }
  },
  directives: {
    'click-outside': {
      bind(el, binding) {
        el.clickOutsideEvent = function(event) {
          if (!(el === event.target || el.contains(event.target))) {
            binding.value(event);
          }
        };
        document.addEventListener('click', el.clickOutsideEvent);
      },
      unbind(el) {
        document.removeEventListener('click', el.clickOutsideEvent);
      }
    }
  }
}
</script>

<style scoped>
.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  transition: background-color 0.2s;
}

.dropdown-trigger:hover {
  background-color: transparent;
}

.dropdown-trigger.active {
  background-color: transparent;
}

.dropdown-menu {
  position: absolute;
  left: 0;
  right: auto;
  top: 100%;
  margin-top: 4px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  width: 100%;
  z-index: 100;
  overflow: hidden;
}

.dropdown-container[data-placement="bottom-left"] .dropdown-menu {
  left: 0;
  right: auto;
}

.dropdown-container[data-placement="top-right"] .dropdown-menu {
  bottom: 100%;
  top: auto;
  margin-top: 0;
  margin-bottom: 4px;
}

.dropdown-container[data-placement="top-left"] .dropdown-menu {
  bottom: 100%;
  top: auto;
  left: 0;
  right: auto;
  margin-top: 0;
  margin-bottom: 4px;
}

.icon-tiny {
  width: 16px;
  height: 16px;
  margin-left: 4px;
  transition: transform 0.2s;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.icon-tiny.rotate {
  transform: rotate(180deg);
}
</style>
