<template>
  <el-dialog
    :visible="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    width="600px"
    custom-class="no-padding-dialog"
    @close="closeClick">
    <span slot="title" class="el-dialog__title">添加邮箱账号</span>
    <div v-loading="loading" class="add-email">
      <!-- 邮箱表单 -->
      <div class="email-form">
        <div class="form-item">
          <label class="form-label">邮箱账号：</label>
          <el-input
            v-model="emailAddress"
            placeholder="请输入邮箱账号"
            autocomplete="off"
            class="form-input" />
        </div>
        <div class="form-item">
          <label class="form-label">邮箱密码：</label>
          <el-input
            v-model="emailPassword"
            type="password"
            autocomplete="off"
            placeholder="请输入邮箱密码"
            class="form-input" />
        </div>
        
        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            type="primary"
            class="save-btn"
            @click="handleSave">立即保存</el-button>
          <el-button
          v-if="emailFlag.addMailAccount"
            type="text"
            class="advanced-btn"
            @click="handleAdvancedSettings">高级设置</el-button>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="divider"></div>

      <!-- 快捷邮箱选择 -->
      <div class="quick-email-options">
        <div 
          class="email-option"
          @click="handleQuickAdd('exchange-international')">
          <div class="email-icon outlook-icon"></div>
          <span class="email-text">添加 Microsoft Exchange 邮箱(Outlook/Hotmail/Office365国际版/Live)</span>
        </div>
        <div 
          class="email-option"
          @click="handleQuickAdd('exchange-domestic')">
          <div class="email-icon outlook-icon"></div>
          <span class="email-text">添加 Microsoft Exchange 邮箱(Office365国内版)</span>
        </div>
        <div 
          class="email-option"
          @click="handleQuickAdd('gmail')">
          <div class="email-icon gmail-icon"></div>
          <span class="email-text">添加Gmail邮箱账号</span>
        </div>
      </div>

      <!-- 帮助部分 -->
      <div class="help-section">
        <div class="help-title">帮助</div>
        <el-button
          type="text"
          class="help-link"
          @click="handleHelpClick">常见邮箱的绑定方法</el-button>
      </div>
    </div>
    <span
      slot="footer"
      class="dialog-footer">
      <el-button @click="closeClick">取消</el-button>
    </span>

    <!-- 高级设置弹框 -->
    <advanced-email-settings
      :from="from"
      v-if="advancedSettingsVisible"
      :advanced-settings-visible.sync="advancedSettingsVisible"
      :initial-data="tempData"
      @success="handleAdvancedSuccess"
      @back-to-quick="handleBackToQuick" />
  </el-dialog>
</template>

<script>
import AdvancedEmailSettings from './AdvancedEmailSettings'
import { addMailAccountAPI,addCommonMailAccountAPI } from '@/api/crm/email'
export default {
  components: {
    AdvancedEmailSettings
  },
  // 添加邮箱账号
  name: 'AddGoal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    from: {
      type: String,
      default: ''
    },
    initialData: {
      type: Object,
      default: () => ({})
    },
    emailFlag:{
      type:Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      emailAddress: '',
      emailPassword: '',
      advancedSettingsVisible:false,
      tempData: {}, // 用于存储初始数据
    }
  },
  watch: {
    visible(value) {
      if (value) {
        this.resetData()
      }
    }
  },
  methods: {
    /**
     * 立即保存
     */
    handleSave() {
      if (!this.emailAddress) {
        this.$message.error('请输入邮箱账号')
        return
      }
      if (!this.emailPassword) {
        this.$message.error('请输入邮箱密码')
        return
      }
      let params = {
        emailAddress: this.emailAddress,
        emailPassword: this.emailPassword
      }
      if(this.from){
        params.userId = this.$store.state.user.userInfo.userId
      }
      this.loading = true
      addCommonMailAccountAPI(params)
        .then(res => {
          this.loading = false
          // this.$store.dispatch('GetMessageNum')
          // 保存成功
          setTimeout(() => {
            this.loading = false
            this.$message.success('邮箱账号添加成功')
            this.$emit('success')
            if(this.from){
              this.$router.push('/manage/email/email-account')
            }
            this.advancedSettingsVisible = false
            this.visible = false;
            // this.closeClick()
          }, 1000)
        })
        .catch((error) => {
          this.$message.success('邮箱账号添加失败')
          if(this.from){
            this.$router.push('/manage/email/email-account')
          }
          this.loading = false
        })
    },

    /**
     * 高级设置
     */
    handleAdvancedSettings() {
      this.advancedSettingsVisible =true;
    },
    handleAdvancedSuccess(){
      this.advancedSettingsVisible = false;
      this.$message.success('高级设置保存成功');
      this.$emit('success');
    },
    handleBackToQuick(){
      this.advancedSettingsVisible =false;
    },
    /**
     * 快捷添加邮箱
     */
    handleQuickAdd(type) {
      const typeMap = {
        'exchange-international': 'Microsoft Exchange 邮箱(国际版)',
        'exchange-domestic': 'Microsoft Exchange 邮箱(国内版)',
        'gmail': 'Gmail邮箱'
      }
      this.$message.info(`正在添加${typeMap[type]}...`)
    },

    /**
     * 帮助链接点击
     */
    handleHelpClick() {
      this.$message.info('正在跳转到帮助页面...')
    },

    /**
     * 关闭弹框
     */
    closeClick() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    /**
     * 重置数据
     */
    resetData() {
      this.emailAddress = ''
      this.emailPassword = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.add-email {
  padding: 20px;
  font-size: 14px;
  color: $--color-text-primary;
}

.email-form {
  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .form-label {
      width: 80px;
      flex-shrink: 0;
      text-align: right;
      margin-right: 15px;
      font-weight: 500;
    }

    .form-input {
      flex: 1;
    }
  }

  .form-actions {
    text-align: center;
    margin-top: 30px;

    .save-btn {
      background-color: #0052CC;
      border-color: #0052CC;
      color: white;
      padding: 10px 30px;
      font-size: 14px;
      margin-right: 20px;

      &:hover {
        background-color: #c53030;
        border-color: #c53030;
      }
    }

    .advanced-btn {
      color: #0052CC;
      font-size: 14px;

      &:hover {
        color: #c53030;
      }
    }
  }
}

.divider {
  height: 1px;
  background-color: #e2e8f0;
  margin: 30px 0;
}

.quick-email-options {
  .email-option {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f7fafc;
    }

    .email-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      flex-shrink: 0;
      border-radius: 4px;
    }

    .outlook-icon {
      background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
      position: relative;

      &::after {
        content: 'O';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
      }
    }

    .gmail-icon {
      background: linear-gradient(135deg, #ea4335 0%, #fbbc04 25%, #34a853 50%, #4285f4 100%);
      position: relative;

      &::after {
        content: 'G';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
      }
    }

    .email-text {
      color: #2d3748;
      font-size: 14px;
    }
  }
}

.help-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;

  .help-title {
    color: #718096;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .help-link {
    color: #3182ce;
    font-size: 14px;
    padding: 0;

    &:hover {
      color: #2c5aa0;
    }
  }
}
</style>
