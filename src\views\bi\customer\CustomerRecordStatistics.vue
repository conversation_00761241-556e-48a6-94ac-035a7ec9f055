<template>
  <div
    v-loading="loading"
    class="main-container">
    <filtrate-handle-view
      title="客户跟进次数分析"
      class="filtrate-bar"
      module-type="customer"
      @load="loading=true"
      @change="searchClick" />
    <div class="content">
      <div class="axis-content">
        <div id="axismain" />
      </div>
      <div class="table-content">
        <div class="handle-bar">
          <el-button
            class="export-btn"
            @click="exportClick">导出</el-button>
        </div>
        <el-table
          v-if="showTable"
          :class="WKConfig.tableStyle.class"
          :stripe="WKConfig.tableStyle.stripe"
          :data="list"
          :cell-class-name="cellClassName"
          :summary-method="getSummaries"
          height="400"
          show-summary
          highlight-current-row
          @row-click="handleRowClick"
          @sort-change="({ prop, order }) => mixinSortFn(list, prop, order)">
          <el-table-column
            v-for="(item, index) in fieldList"
            :key="index"
            :prop="item.field"
            :label="item.name"
            sortable="custom"
            align="center"
            header-align="center"
            show-overflow-tooltip />
        </el-table>
      </div>
    </div>

    <!-- 汇报列表 (详情)-->
    <report-list
      :show.sync="reportListShow"
      :title="reportData.title"
      :placeholder="reportData.placeholder"
      :crm-type="reportData.crmType"
      :request="reportData.request"
      :params="reportData.params"
      :record-request="reportData.recordRequest"
      :paging="reportData.paging"
      :sortable="reportData.sortable" />

    <record-list
      v-if="recordShow"
      :crm-type="rowType"
      :request="recordRequest"
      :params="recordParams"
      @handle="getList"
      @hide="recordShow = false" />
  </div>
</template>

<script>
import BaseMixin from '../mixins/Base'
import SortMixin from '../mixins/Sort'
import SummaryMixin from '../mixins/Summary'
import DetailMixin from '@/views/bi/mixins/Detail'
import RecordList from '@/views/crm/workbench/components/ReportList/components/RecordList'

import * as echarts from 'echarts'

import {
  biCustomerRecordTimesAPI,
  biCustomerRecordListAPI,
  biCustomerRecordListExportAPI,
  biCustomerFollowListAPI
} from '@/api/bi/customer'
import { crmActivityListAPI } from '@/api/crm/common'

export default {
  /** 客户跟进次数分析 */
  name: 'CustomerRecordStatistics',
  components: {
    RecordList
  },
  mixins: [BaseMixin, SortMixin, SummaryMixin, DetailMixin],
  data() {
    return {
      loading: false,
      axisOption: null,

      list: [],

      postParams: {}, // 筛选参数
      dataIndex: null,

      axisList: [],
      fieldList: [
        { field: 'realname', name: '员工姓名' },
        { field: 'recordNum', name: '跟进次数' },
        { field: 'outSignRecordNum', name: '外勤签到次数' },
        { field: 'customerNum', name: '跟进客户数' },
        { field: 'recordNumRate', name: '客户跟进占比' }
      ],
      // 可以点击的数据
      detailFields: [
        {
          name: 'recordNum',
          list: [],
          followType: 'times',
          fieldType: 'followTimes',
          request: crmActivityListAPI
        },
        {
          name: 'customerNum',
          fieldType: 'followCustomes',
          customerNum: true,
          list: [],
          request: biCustomerFollowListAPI
        }
        // {
        //   name: 'recordNumRate',
        //   last: true,
        //   list: [{
        //     formType: 'user',
        //     name: 'ownerUserId',
        //     type: 3,
        //     values: []
        //   }],
        //   request: biCustomerDetailListAPI
        // }
      ],

      recordShow: false,
      rowType: 'record',
      recordRequest: crmActivityListAPI,
      recordParams: {
        activityType: 2,
        dateFilter: '',
        isOa: true,
        limit: 15,
        page: 1,
        queryType: 1,
        recordType: 1,
        userList: []
      }
    }
  },
  mounted() {
    this.initAxis()
  },
  methods: {
    getList() {},
    /**
     * 搜索点击
     */
    searchClick(params) {
      this.postParams = params
      this.getDataList()
      this.getRecordList()
    },
    /**
     * 图表数据
     */
    getDataList() {
      this.loading = true
      biCustomerRecordTimesAPI(this.postParams)
        .then(res => {
          this.loading = false
          this.axisList = res.data || []

          const customerCounts = []
          const dataCounts = []
          const xAxis = []
          for (let index = 0; index < res.data.length; index++) {
            const element = res.data[index]
            customerCounts.push(element.customerNum)
            dataCounts.push(element.recordNum)
            xAxis.push(element.type)
          }
          this.axisOption.xAxis[0].data = xAxis
          this.axisOption.series[0].data = customerCounts
          this.axisOption.series[1].data = dataCounts
          this.chartObj.setOption(this.axisOption, true)
        })
        .catch(() => {
          this.loading = false
        })
    },
    /**
     * 获取相关列表
     */
    getRecordList(dataIndex) {
      this.dataIndex = dataIndex
      this.list = []
      this.loading = true
      biCustomerRecordListAPI(this.postParams)
        .then(res => {
          this.loading = false
          this.list = res.data || []
        })
        .catch(() => {
          this.loading = false
        })
    },

    /** 柱状图 */
    initAxis() {
      var chartObj = echarts.init(document.getElementById('axismain'))

      var option = {
        color: this.echartLineBarColors,
        toolbox: this.toolbox,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: ['跟进客户数', '跟进次数'],
          ...this.chartDefaultOptions.legend
        },
        grid: this.chartDefaultOptions.grid,
        xAxis: [
          {
            type: 'category',
            data: [],
            ...this.chartXAxisStyle
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '跟进客户数',
            ...this.getChartYAxisStyle({
              axisLabel: {
                formatter: '{value} 个'
              }
            })
          },
          {
            type: 'value',
            name: '跟进次数',
            ...this.getChartYAxisStyle({
              axisLabel: {
                formatter: '{value} 次'
              },
              splitLine: {
                show: true
              }
            })
          }
        ],
        series: [
          {
            name: '跟进客户数',
            type: 'bar',
            yAxisIndex: 0,
            barMaxWidth: 15,
            data: []
          },
          {
            name: '跟进次数',
            type: 'bar',
            yAxisIndex: 1,
            barMaxWidth: 15,
            data: []
          }
        ]
      }

      chartObj.setOption(option, true)
      chartObj.on('click', params => {
        // seriesIndex	1：跟进客户数 2:跟进次数  dataIndex 具体的哪条数据
        this.getRecordList(params.dataIndex)
      })
      this.axisOption = option
      this.chartObj = chartObj
    },

    /**
     * 导出点击
     */
    exportClick() {
      this.requestExportInfo(biCustomerRecordListExportAPI, this.postParams)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../styles/detail.scss";
</style>
