
import ReportList from '@/views/crm/workbench/components/ReportList'
import crmTypeModel from '@/views/crm/model/crmTypeModel'

import {
  biBusinessWinOrFailDetailListAPI
} from '@/api/bi/business'

import moment from 'moment'

export default {
  data() {
    return {
      // 弹窗数据
      fieldReportList: null,
      reportListShow: false,
      reportData: {
        title: '',
        placeholder: '',
        crmType: '',
        request: null,
        params: null,
        paging: true,
        sortable: false
      },
      search: '',
      isSeas: false
    }
  },
  components: {
    ReportList
  },
  methods: {
    /**
     * @description: 查看详情
     * @param {*} row
     * @param {*} column
     * @param {*} event
     * @return {*}
     */
    handleRowClick(row, column, event) {
      if (column.type === 'selection') {
        return // 多选布局不能点击
      }
      const array = this.detailFields.map((field) => field.name)
      // if (array.includes(column.property) && row[column.property] > 0) {
      if (array.includes(column.property)) {
        const findRes = this.detailFields.find(field => field.name == column.property)
        if (findRes) {
          const title = column.label
          const copyData = { ...findRes, title }

          let params = null
          if (findRes.fieldType === 'followTimes') {
            this.reportData.crmType = 'customer'
            params = {
              crmType: 2,
              label: 2,
              queryType: 0,
              type: 2,
              ...this.postParams
            }
          } else if (findRes.fieldType === 'followCustomes') {
            this.reportData.crmType = 'customer'
            params = {
              type: 2,
              userList: [row.userId],
              search: '',
              dataType: this.postParams.dataType,
              dateFilter: this.postParams.dateFilter
            }
            if (findRes.customerNum) {
              params.searchList = [{
                formType: 'user',
                name: 'ownerUserId',
                type: 3,
                values: [row.userId]
              }]
              params.dataType = 0
              params.category = 2
            }
          } else if (this.type === 'product') {
            params = {
              search: '',
              id: row.productId,
              ...this.postParams
            }
          } else {
            params = this.getDetailParams(findRes, row)
            if (['putInNum'].includes(findRes.name)) {
              params.id = row.userId || row.ownerUserId
            }
            if (findRes.crmType === 'product') {
              params.id = row.productId
            }
          }
          delete copyData.name
          this.reportData = {
            ...this.reportData,
            ...copyData,
            params
          }

          // if (findRes.difType === 'product') {
          //   this.reportData.params.searchList.push({
          //     name: 'ownerUserId',
          //     formType: 'user',
          //     type: 3,
          //     values: [row.productId]
          //   })
          // }

          if (findRes.flowName && row.isEnd) {
            this.reportData.params.categoryId = this.postParams.typeId
            this.reportData.params.dateFilter = this.postParams.dateFilter
            this.reportData.request = biBusinessWinOrFailDetailListAPI
          }

          if (findRes.isBusiness) {
            if (this.postParams.dataType == 0) {
              this.reportData.params.deptList = this.postParams.deptList
              this.reportData.params.userList = this.postParams.userList
            }
          }

          if (findRes.fieldType === 'followTimes') {
            this.recordParams.dateFilter = this.postParams.dateFilter
            if (this.recordParams.dateFilter === 'custom') {
              this.recordParams.startDate = this.postParams.startDate
              this.recordParams.endDate = this.postParams.endDate
            }
            if (findRes.followType !== 'times') {
              this.recordParams.dataType = this.postParams.dataType
            }
            this.recordParams.userList = [row.userId]
            this.recordShow = true
          } else {
            this.reportListShow = true
          }

          if (this.reportData.params.dateFilter === 'custom') {
            this.reportData.params.startDate = this.postParams.startDate
            this.reportData.params.endDate = this.postParams.endDate
          }

          if (['customer', 'address', 'address', 'product'].includes(this.type)) {
            if (this.postParams.dataType == 0) {
              this.reportData.params.deptList = this.postParams.deptList
              this.reportData.params.userList = this.postParams.userList
            }
          }

          if (findRes.crmType == 'invoice') {
            if (this.postParams.dataType == 0) {
              this.reportData.params.deptList = this.postParams.deptList
              this.reportData.params.userList = this.postParams.userList
            }
          }

          if (['popular', 'productPopular'].includes(findRes.fieldType)) {
            this.reportData.params.dateFilter = this.postParams.dateFilter
            if (this.reportData.params.dateFilter === 'custom') {
              this.reportData.params.startDate = this.postParams.startDate
              this.reportData.params.endDate = this.postParams.endDate
            }
            if (this.postParams.dataType == 0) {
              this.reportData.params.deptList = this.postParams.deptList
            }
            if (findRes.fieldType == 'popular') {
              if (!this.reportData.params.searchType) {
                this.reportData.params.dataType = 0
              }
              this.reportData.params.userList = [row.ownerUserId]
            } else {
              if (this.postParams.dataType == 0) {
                this.reportData.params.deptList = this.postParams.deptList
                this.reportData.params.userList = this.postParams.userList
              }
              this.reportData.params.id = row.productId
            }

            delete this.reportData.params.searchList
          }
        }
      } else {
        this.reportListShow = false
      }
    },
    /**
     * @description: 获取详情参数
     * @param {*} field
     * @return {*}
     */
    getDetailParams(field, row) {
      this.reportData.crmType = field.crmType || 'customer'

      const type = this.isSeas ? crmTypeModel.pool : crmTypeModel[this.reportData.crmType]

      let lastDate = ''
      let values = null
      if (field.padDate) {
        const [y, m] = row.type.split('-')
        lastDate = moment(`${y}-${m}-01`).endOf('month').format('YYYY-MM-DD')
        values = [row.type, lastDate]
      } else {
        values = this.postParams.dateFilter == 'custom'
          ? [this.postParams.startDate, this.postParams.endDate || lastDate]
          : [this.postParams.dateFilter]
      }

      const searchList = this.getSearchList(field, values, row)

      // if (crmType && crmType == 'product') {
      //   field.params.id = row.productId
      // }

      return {
        search: this.search,
        type: row.isEnd ? row.isEnd : type,
        searchList,
        dataType: this.postParams.dataType,
        ...field.params
      }
    },
    /**
     * 获取 searchList
     */
    getSearchList(field, values, row) {
      const { timeName, list, last, flowName, isBusiness } = field

      let lists = []

      if (!flowName) {
        lists = [{
          formType: ['orderDate', 'returnTime', 'realInvoiceDate'].includes(timeName) ? 'date' : 'datetime',
          name: timeName || 'createTime',
          type: 14,
          values: values
        }, ...(list || [])]
      }

      if (flowName && isBusiness && !row.isEnd) {
        const businessName = this.businessItem[0]
        lists = [{
          formType: 'datetime',
          name: 'createTime',
          type: 14,
          values: values
        }, {
          formType: 'business_cause',
          name: 'flowName',
          group: true,
          type: 1,
          values: [businessName.flowName]
        }, {
          formType: 'business_cause',
          name: 'settingName',
          stage: true,
          type: 1,
          values: [row.settingName]
        }]
      }

      // eslint-disable-next-line no-unused-vars
      for (const item of lists) {
        if (item.name === 'ownerUserId' || item.name === 'createUserId') {
          item.values = [row.userId || row.ownerUserId]
        } else if (item.name === 'isEnd') {
          item.values = [row.isEnd]
        } else if (item.name === 'address') {
          item.values = [row.type]
        } else if (field.fieldType === 'customerRank' && item.formType === 'user') {
          item.values = [row.userId]
        }
      }

      if (last) {
        lists.push({
          formType: 'datetime',
          name: 'lastTime',
          type: 14,
          values: values
        })
      }

      return lists
    },

    /**
     * 通过回调控制class
     */
    cellClassName({ row, column, rowIndex, columnIndex }) {
      const array = this.detailFields.map((field) => field.name)
      if (array.includes(column.property)) {
        return 'can-visit--underline'
      } else {
        return ''
      }
    }
  }
}
