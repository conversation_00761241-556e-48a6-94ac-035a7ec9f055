// Figures out the event we are using with the bound element
// https://github.com/dhershman1/vue-debounce
// https://blog.csdn.net/sinat_36146776/article/details/84953131

import { debounce } from 'throttle-debounce'
import { on, off } from '@/utils/dom'

export default {
  name: 'debounce',
  install(Vue, { defaultTime = 300, defaultEvent = 'click' } = {}) {
    Vue.directive('debounce', {
      bind(el, binding) {
        let cb = null
        let delay = defaultTime
        let eventName = defaultEvent
        const immediate = binding.modifiers.immediate

        if (typeof binding.value === 'function') {
          cb = binding.value
        } else if (
          Array.isArray(binding.value) &&
          typeof binding.value[0] === 'function'
        ) {
          cb = binding.value[0]
          delay = binding.value[1] || defaultTime
        } else {
          console.warn('[v-debounce] expects a function or [function, delay] as value')
          return
        }

        if (binding.arg) {
          eventName = binding.arg
        }

        let invoked = false
        const debounced = debounce(delay, function (...args) {
          if (immediate && !invoked) {
            invoked = true
            cb.apply(binding.instance, args)
          } else if (!immediate) {
            cb.apply(binding.instance, args)
          }
        })

        el.__debouncedHandler__ = debounced
        el.__debouncedEvent__ = eventName
        on(el, eventName, debounced)
      },
      unbind(el) {
        off(el, el.__debouncedEvent__ || 'click', el.__debouncedHandler__)
        delete el.__debouncedHandler__
        delete el.__debouncedEvent__
      }
    })
  }
}

