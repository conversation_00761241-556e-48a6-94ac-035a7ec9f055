import axios from 'axios'
import cache from './cache'
import Lockr from 'lockr'
import store from '@/store'
import Cookies from 'js-cookie'
import { getCookiesDomain } from '@/utils'

/** 移除授权信息 */
export function removeAuth(props = { clearCookies: false }) {
  return new Promise((resolve, reject) => {
    // if (props.clearCookies) {
    //   Cookies.remove('User', { path: '/', domain: '.72crm.com' })
    // }
    cache.rmAxiosCache()
    store.commit('SET_ALLAUTH', null)
    delete axios.defaults.headers['Admin-Token']
    resolve(true)
  })
}

/** 注入授权信息 */
export function addAuth(adminToken) {
  return new Promise((resolve, reject) => {
    axios.defaults.headers['Admin-Token'] = adminToken
    // store.dispatch('SystemLogoAndName')
    resolve(true)
  })
}

/** 获取授权信息 */
export function getAuth() {
  return new Promise((resolve, reject) => {
    if (axios.defaults.headers['Admin-Token']) {
      resolve()
    } else {
      // if (Lockr.get('Admin-Token') || Cookies.get('User')) {
      const cookieAdminToken = Cookies.get('AdminToken')
      let lockAdminToken = Lockr.get('Admin-Token')
      // 状态码 1007 下 会忽略下一次的cookies信息读取
      // 重点 数据以cookie为准，只要cookie有值，或者与localstorage不同，覆盖localstorage
      if (window.sessionStorage.wkIgnoreCookies !== true && !!cookieAdminToken && (lockAdminToken !== cookieAdminToken)) {
        Lockr.set('Admin-Token', cookieAdminToken)
        lockAdminToken = cookieAdminToken
      }

      // 本地有lock
      if (!!lockAdminToken && !cookieAdminToken) {
        const domain = getCookiesDomain()
        Cookies.set('AdminToken', lockAdminToken, { domain: domain, expires: 365 })
      }

      if (lockAdminToken) {
        cache.updateAxiosCache()
        store.dispatch('GetUserInfo').then(() => {
          // 不忽略Cookies
          window.sessionStorage.wkIgnoreCookies = false
          resolve()
        }).catch(() => {
          reject()
        })
      } else {
        reject()
      }
    }
  })
}
