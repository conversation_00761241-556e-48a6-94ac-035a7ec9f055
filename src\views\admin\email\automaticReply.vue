<template>
  <div v-loading="loading" class="automatic-reply-container">
    <!-- 页面头部 -->
    <xr-header label="个人假期自动回复" />
    
    <div class="main-body">
      <!-- 自动回复开关 -->
      <div class="switch-section">
        <span class="section-label">自动回复</span>
        <el-switch
          v-model="autoReplyEnabled"
          active-color="#13ce66"
          inactive-color="#dcdfe6">
        </el-switch>
      </div>

      <!-- 日期范围选择 -->
      <div class="form-section">
        <div class="form-item">
          <span class="item-label">日期范围：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 350px;"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </div>

        <!-- 回复频率 -->
        <div class="form-item">
          <span class="item-label">回复频率：</span>
          <div class="frequency-selector">
            <span class="frequency-text">同一个邮箱给你发送多封邮件时，</span>
            <el-select v-model="replyFrequency" placeholder="请选择" style="width: 120px;">
              <el-option
                v-for="item in frequencyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <span class="frequency-text">天内最多对该邮箱自动回复一次</span>
          </div>
        </div>

        <!-- 回复内容 -->
        <div class="form-item">
          <span class="item-label">回复内容：</span>
          <div class="editor-container">
            <tinymce
              ref="signatureEditor"
              v-model="replyContent"
              :height="300"
              :init="editorInit"
              @input="handleContentChange" />
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="button-section">
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import XrHeader from '@/components/XrHeader'
import Tinymce from '@/components/Tinymce'

export default {
  name: 'AutomaticReply',
  components: {
    XrHeader,
    Tinymce
  },
  data() {
    return {
      loading: false,
      autoReplyEnabled: false,
      dateRange: [],
      replyFrequency: '3',
      frequencyOptions: [
        { value: '1', label: '1' },
        { value: '3', label: '3' },
        { value: '7', label: '7' },
        { value: '30', label: '30' }
      ],
      replyContent: '',
      editorFont: 'arial',
      editorFontSize: '14px',
            // 富文本编辑器配置
      editorInit: {
        height: 300,
        menubar: false,
        statusbar: false,
        placeholder: '请输入回复内容...',
        content_style: 'body { font-family: Arial, sans-serif; font-size: 14px; }',
        toolbar: 'undo redo | bold italic underline strikethrough | fontselect fontsizeselect | forecolor backcolor | alignleft aligncenter alignright | bullist numlist | link image | removeformat',
        plugins: 'link image lists textcolor colorpicker',
        branding: false,
        elementpath: false,
        resize: false
      }
    }
  },
  mounted() {
    this.getSettings()
  },
  methods: {
    // 获取自动回复设置
    async getSettings() {
      try {
        this.loading = true
        // 这里应该调用实际的API获取设置
        // const { data } = await getAutoReplySettingsAPI()
        // 模拟数据
        setTimeout(() => {
          this.autoReplyEnabled = false
          this.dateRange = []
          this.replyFrequency = '3'
          this.replyContent = ''
          this.loading = false
        }, 500)
      } catch (error) {
        console.error('获取自动回复设置失败', error)
        this.loading = false
        this.$message.error('获取自动回复设置失败')
      }
    },
    handleContentChange(){

    },
    // 保存自动回复设置
    async saveSettings() {
      if (this.autoReplyEnabled && (!this.dateRange || this.dateRange.length !== 2)) {
        this.$message.warning('请选择日期范围')
        return
      }
      
      if (this.autoReplyEnabled && !this.replyContent) {
        this.$message.warning('请输入回复内容')
        return
      }
      
      try {
        this.loading = true
        // 这里应该调用实际的API保存设置
        // await saveAutoReplySettingsAPI({
        //   enabled: this.autoReplyEnabled,
        //   startDate: this.dateRange ? this.dateRange[0] : null,
        //   endDate: this.dateRange ? this.dateRange[1] : null,
        //   frequency: this.replyFrequency,
        //   content: this.replyContent
        // })
        
        // 模拟保存成功
        setTimeout(() => {
          this.loading = false
          this.$message.success('保存成功')
        }, 500)
      } catch (error) {
        console.error('保存自动回复设置失败', error)
        this.loading = false
        this.$message.error('保存自动回复设置失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.automatic-reply-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .main-body {
    flex: 1;
    padding: 20px;
    background-color: #fff;
    margin: 16px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .switch-section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    .section-label {
      margin-right: 10px;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .form-section {
    .form-item {
      margin-bottom: 20px;
      display: flex;   
      
      .item-label {
        display: block;
        font-weight: 500;
        line-height: 30px;
      }
      .frequency-selector {
        display: flex;
        align-items: center;
        
        .frequency-text {
          margin: 0 5px;
        }
      }
      
      .editor-container {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        
        .editor-toolbar {
          display: flex;
          padding: 8px;
          border-bottom: 1px solid #dcdfe6;
          background-color: #f5f7fa;
          
          .toolbar-buttons {
            display: flex;
            margin-left: 10px;
            
            .el-button-group {
              margin-right: 10px;
            }
          }
        }
        
        .el-textarea {
          .el-textarea__inner {
            border: none;
            border-radius: 0;
          }
        }
      }
    }
  }
  
  .button-section {
    margin-top: 30px;
    margin-left: 70px;
  }
}
</style>