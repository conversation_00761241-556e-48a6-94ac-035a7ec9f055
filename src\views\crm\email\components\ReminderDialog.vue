<template>
  <div class="reminder-dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="reminder-dialog" @click.stop>
      <div class="reminder-dialog-header">
        <h3>设置提醒</h3>
        <button class="close-dialog" @click="cancel">
          <x-icon class="icon-small" />
        </button>
      </div>
      <div class="reminder-dialog-body">
        <div class="form-group">
          <label>提醒时间</label>
          <div class="reminder-options">
            <div
              v-for="option in reminderOptions"
              :key="option.value"
              class="reminder-option"
              :class="{ 'active': selectedOption === option.value }"
              @click="selectedOption = option.value"
            >
              {{ option.label }}
            </div>
          </div>
        </div>

        <div class="form-group" v-if="selectedOption === 'custom'">
          <label>自定义时间</label>
          <div class="custom-time">
            <input type="date" v-model="customDate" class="date-input" />
            <input type="time" v-model="customTime" class="time-input" />
          </div>
        </div>

        <div class="form-group">
          <label>提醒内容</label>
          <textarea
            v-model="reminderNote"
            placeholder="添加提醒备注（可选）"
            class="reminder-note"
          ></textarea>
        </div>
      </div>
      <div class="reminder-dialog-footer">
        <button class="cancel-btn" @click="cancel">取消</button>
        <button class="confirm-btn" @click="confirm">保存</button>
      </div>
    </div>
  </div>
</template>

<script>
import { XIcon } from 'lucide-vue'

export default {
  name: 'ReminderDialog',
  components: {
    XIcon
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    emailSubject: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      reminderOptions: [
        { label: '30分钟后', value: '30min' },
        { label: '1小时后', value: '1hour' },
        { label: '今天晚上', value: 'tonight' },
        { label: '明天上午', value: 'tomorrow' },
        { label: '下周一', value: 'nextMonday' },
        { label: '自定义', value: 'custom' }
      ],
      selectedOption: '30min',
      customDate: this.getTodayDate(),
      customTime: this.getCurrentTime(),
      reminderNote: ''
    }
  },
  mounted() {
    // 默认设置提醒内容为邮件主题
    this.reminderNote = `提醒：${this.emailSubject}`;
  },
  methods: {
    getTodayDate() {
      const today = new Date();
      return today.toISOString().split('T')[0];
    },
    getCurrentTime() {
      const now = new Date();
      return `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    },
    getReminderTime() {
      const now = new Date();

      switch(this.selectedOption) {
        case '30min':
          return new Date(now.getTime() + 30 * 60000);
        case '1hour':
          return new Date(now.getTime() + 60 * 60000);
        case 'tonight':
          const tonight = new Date(now);
          tonight.setHours(20, 0, 0, 0);
          return tonight;
        case 'tomorrow':
          const tomorrow = new Date(now);
          tomorrow.setDate(tomorrow.getDate() + 1);
          tomorrow.setHours(9, 0, 0, 0);
          return tomorrow;
        case 'nextMonday':
          const nextMonday = new Date(now);
          nextMonday.setDate(nextMonday.getDate() + (8 - nextMonday.getDay()) % 7);
          nextMonday.setHours(9, 0, 0, 0);
          return nextMonday;
        case 'custom':
          const customDateTime = new Date(`${this.customDate}T${this.customTime}`);
          return customDateTime;
        default:
          return new Date(now.getTime() + 30 * 60000);
      }
    },
    confirm() {
      const reminderTime = this.getReminderTime();
      this.$emit('confirm', {
        time: reminderTime,
        note: this.reminderNote
      });
    },
    cancel() {
      this.$emit('cancel');
    },
    handleOverlayClick() {
      this.cancel();
    }
  }
}
</script>

<style scoped>
.reminder-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.reminder-dialog {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 450px;
  max-width: 90vw;
  overflow: hidden;
}

.reminder-dialog-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reminder-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-dialog {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
}

/* 确保SVG图标正确显示 */
::v-deep svg {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.reminder-dialog-body {
  padding: 20px 16px;
  font-size: 14px;
  color: #333;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.reminder-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.reminder-option {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.reminder-option:hover {
  border-color: #0052CC;
}

.reminder-option.active {
  background-color: #e6f7ff;
  border-color: #0052CC;
  color: #0052CC;
}

.custom-time {
  display: flex;
  gap: 8px;
}

.date-input, .time-input {
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.date-input {
  flex: 2;
}

.time-input {
  flex: 1;
}

.reminder-note {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  min-height: 80px;
  resize: vertical;
}

.reminder-dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #e6e9ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn, .confirm-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
}

.cancel-btn {
  background-color: #fff;
  color: #333;
}

.confirm-btn {
  background-color: #0052CC;
  color: white;
  border-color: #0052CC;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
}

.confirm-btn:hover {
  opacity: 0.9;
}
</style>
