<template>
  <slide-view
    v-empty="!canShowDetail"
    :listener-ids="listenerIDs"
    :no-listener-ids="noListenerIDs"
    :no-listener-class="noListenerClass"
    :body-style="{padding: 0, height: '100%'}"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限"
    @afterEnter="viewAfterEnter"
    @close="hideView">
    <div
      ref="crmDetailMain"
      v-loading="loading"
      class="detail-main no-padding">
      <flexbox
        v-if="canShowDetail && detailData"
        direction="column"
        align="stretch"
        class="d-container">
        <c-r-m-detail-head
          :id="id"
          :class="{'is-shadow': bodyIsScroll}"
          :detail="detailData"
          :crm-type="crmType"
          :page-list="pageList"
          :tag-info="tagInfo"
          @pageChange="pageChange"
          @handle="detailHeadHandle"
          @close="hideView" />

        <div class="d-container-body" @scroll="bodyScroll">
          <detail-head-base :list="headDetails" />
          <relative-stage-records
            v-if="statusShow"
            :id="id"
            :crm-type="crmType"
            :detail="detailData"
            @handle="detailHeadHandle"
          />

          <el-tabs
            v-model="tabCurrentName"
            nav-mode="more"
            class="top-padding">
            <el-tab-pane
              v-for="(item, index) in tabNames"
              :key="index"
              :label="item.label"
              :name="item.name"
              lazy>
              <template slot="label">
                <el-badge
                  :value="item.num"
                  :hidden="item.num <= 0"
                  type="undefined">
                  {{ item.label }}
                </el-badge>
              </template>
              <component
                :is="item.name"
                :id="id"
                :ref="item.name"
                :detail="detailData"
                :crm-type="crmType"
                @handle="detailHeadHandle" />
            </el-tab-pane>
          </el-tabs>

        </div>
      </flexbox>
    </div>

    <c-r-m-all-create
      v-if="isCreate"
      :action="{type: 'update', id: id, batchId: detailData.batchId, editDetail: detailData}"
      :crm-type="crmType"
      @save-success="editSaveSuccess"
      @close="isCreate=false" />
  </slide-view>
</template>

<script>
import { crmReturnVisitReadAPI } from '@/api/crm/visit'

import SlideView from '@/components/SlideView'
import CRMDetailHead from '../components/CRMDetailHead'
import RelativeStageRecords from '../components/RelativeStageRecords' // 阶段记录
import CRMEditBaseInfo from '../components/CRMEditBaseInfo' // 产品基本信息
import RelativeFiles from '../components/RelativeFiles' // 相关附件
import RelativeHandle from '../components/RelativeHandle' // 相关操作

import CRMAllCreate from '../components/CRMAllCreate' // 新建页面
import Sections from '../components/Sections'

import DetailMixin from '../mixins/Detail'

export default {
  name: 'VisitDetail', // 回访详情
  components: {
    SlideView,
    CRMDetailHead,
    RelativeStageRecords,
    CRMEditBaseInfo,
    RelativeFiles,
    RelativeHandle,
    CRMAllCreate,
    Sections
  },
  mixins: [DetailMixin],
  props: {
    // 详情信息id
    id: [String, Number],
    // 监听的dom 进行隐藏详情
    listenerIDs: {
      type: Array,
      default: () => {
        return ['crm-main-container']
      }
    },
    // 不监听
    noListenerIDs: {
      type: Array,
      default: () => {
        return []
      }
    },
    noListenerClass: {
      type: Array,
      default: () => {
        return ['el-table__body']
      }
    }
  },
  data() {
    return {
      // 展示加载loading
      loading: false,
      crmType: 'visit',
      headDetails: [],
      tabCurrentName: 'CRMEditBaseInfo',
      // 编辑操作
      isCreate: false
    }
  },
  computed: {
    tabNames() {
      var tempsTabs = [
        { label: '详细资料', name: 'CRMEditBaseInfo' },
        { label: '附件', num: this.tabsNumber.fileCount, name: 'RelativeFiles' },
        { label: '操作记录', name: 'RelativeHandle' }
      ]
      // if (this.statusShow) {
      //   tempsTabs.unshift({ label: '阶段记录', name: 'RelativeStageRecords' })
      // }
      return tempsTabs
    }
  },
  mounted() {},
  methods: {
    /**
     * 详情
     */
    getDetial() {
      this.loading = true
      crmReturnVisitReadAPI(this.id)
        .then(res => {
          this.loading = false
          const resData = res.data || {}
          this.detailData = resData

          this.headDetails = [
            { title: '客户名称', value: resData.customerName },
            { title: '回访时间', value: resData.visitTime },
            { title: '回访人', value: resData.ownerUserName },
            { title: '回访形式', value: resData.returnVisitType }
          ]
        })
        .catch(() => {
          this.loading = false
          this.hideView()
        })
    },

    /**
     * 关闭
     */
    hideView() {
      this.$emit('hide-view')
    },
    /**
     * 预览图片
     */
    previewImage(list, index) {
      this.$wkPreviewFile.preview({
        index: index,
        data: list
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/crmdetail.scss";

.image {
  color: $--color-text-primary;

  &-info {
    margin: 10px 25px 0;

    &__label {
      font-size: 13px;
    }

    &__list {
      overflow-x: auto;
      white-space: nowrap;

      img {
        margin-top: 15px;
        cursor: pointer;
        border-radius: $--border-radius-base;
      }

      img + img {
        margin-left: 20px;
      }

      .main-img {
        width: 100px;
        height: 76px;
      }

      .detial-img {
        width: 100px;
        height: 80px;
      }
    }
  }

  .no-img {
    margin: 50px 0;
    color: $--color-text-regular;
    text-align: center;
  }
}
</style>
