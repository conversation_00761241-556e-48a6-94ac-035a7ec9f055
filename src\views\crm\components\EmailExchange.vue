<template>
  <div class="mail-page">
    <!-- 联系人信息区域 -->
    <div class="contact-info-section" v-if="customerInfo">
      <!-- 有联系人信息时显示 -->
      <div class="contact-card" v-if="!isEmpty(customerInfo)">
        <div class="contact-avatar">
          <div class="default-avatar">
            <div class="avatar-silhouette"></div>
          </div>
        </div>
        <div class="contact-details">
          <div class="contact-name">
            {{ extractUserName(customerInfo.email) }}
            <span class="vip-badge">主</span>
          </div>
          <div class="contact-email">
            <i class="el-icon-message"></i>
            {{ customerInfo.email }}
          </div>
          <div class="contact-company">
            <i class="el-icon-office-building"></i>
            {{ customerInfo.customerName }}
          </div>
        </div>
        <div class="contact-actions">
          <el-button type="text" size="small" @click="showMoreInfo">
            更多信息
            <i class="el-icon-arrow-right"></i>
          </el-button>
        </div>
      </div>

      <!-- 无联系人信息时显示 -->
      <div class="contact-card contact-not-found" v-else>
        <div class="warning-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="contact-email-link">
          <a :href="`mailto:${currentEmailAddress}`" class="email-link">
            {{ currentEmailAddress }}
          </a>
          <i class="el-icon-message email-icon"></i>
        </div>
        <div class="not-found-message">
          {{ extractUserName(currentEmailAddress) }}是一个陌生客户，抓住机会。
        </div>
        <div class="action-message">
          立即【新增客户】或【新增联系人】
        </div>
        <div class="action-buttons">
          <el-button type="danger" size="medium" @click="addNewCustomer">
            新增客户
          </el-button>
          <el-button type="danger" size="medium" @click="addNewContact">
            新增联系人
          </el-button>
        </div>
      </div>
    </div>

    <EmailList @email-selected="handleEmailSelected" :userInfo="userInfo" :emailList="tableData"
      :pagination="pagination" :autoLoad="false" @page-change="handlePageChange" data-parent-height="true"
      class="customer-email-list" />
    <el-drawer :title="selectedEmail ? selectedEmail.subject || '邮件详情' : '邮件详情'" :visible.sync="drawer"
      :direction="direction" :before-close="handleClose" size="40%" :modal="false" :show-close="true">
      <!-- 加载状态 -->
      <div v-if="detailLoading" class="loading-container">
        <div class="loading-spinner">
          <i class="el-icon-loading"></i>
          <span>正在加载邮件详情...</span>
        </div>
      </div>

      <!-- 邮件详情 -->
      <EmailDetails v-else-if="selectedEmailDetail" :selectedEmailDetail="selectedEmailDetail"
        :filteredEmails="[selectedEmailDetail]" :showTrackDetail="false" @reply="handleReply"
        @reply-all="handleReplyAll" @forward="handleForward" @archive="handleArchive" @tag="handleTag"
        @distribute="handleDistribute" @star="handleStar" @delete="handleDelete" @set-reminder="handleSetReminder"
        @navigate="handleNavigate" @fullscreen="handleFullscreen" @add-newclues="handleAddNewClues"
        @add-salesorder="handleAddSalesOrder" @view-new-tab="handleViewNewTab" @edit-draft="handleEditDraft"
        @remove-tag="handleRemoveTag" @remove-from-archive="handleRemoveFromArchive" />
      <div v-else class="no-email-selected">
        <div class="no-email-icon">📧</div>
        <div class="no-email-text">请选择一封邮件查看详情</div>
      </div>
    </el-drawer>

    <!-- 新增客户弹窗 -->
    <customer-create append-to-body v-if="showCreateCustomer" :crm-type="crmType" :action="createAction"
      @close="closeCreateCustomer" @save-success="handleCreateSuccess" />

    <!-- 新增联系人弹窗 -->
    <contacts-create append-to-body v-if="showCreateContact" :action="createAction" @close="closeCreateContact"
      @save-success="handleContactCreateSuccess" />

    <!-- 相关详情页面 -->
    <CRMAllDetail v-if="showDview" :id.sync="customerInfo && customerInfo.customerId" :visible.sync="showDview" :crm-type="crmType"
      :model-data="{}" class="d-view" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getCustomerMailsAPI, queryEmailDetailsAPI, saveEmailReadStatusAPI } from '@/api/crm/email'
import EmailList from '../email/components/EmailList.vue'
import EmailDetails from '../email/components/EmailDetails.vue'
import CustomerCreate from '@/views/crm/customer/Create'
import ContactsCreate from '@/views/crm/contacts/Create'
const CRMAllDetail = () => import('@/views/crm/components/CRMAllDetail')
import { isEmpty } from '@/utils/types'

export default {
  name: 'EmailExchange',
  components: {
    EmailList,
    EmailDetails,
    CustomerCreate,
    ContactsCreate,
    CRMAllDetail
  },
  props: {
    // 从客户详情页面传入的客户数据
    detail: {
      type: Object,
      default: () => ({})
    },
    customerInfo: {
      type: Object,
      default: null
    },
    // 客户ID
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      filters: {
        customerName: '',
        receiver: '',
        dateRange: [],
        status: ''
      },
      crmType:'customer',
      drawer: false,
      direction: 'rtl',
      selectedEmail: null, // 当前选中的邮件（列表中的简单数据）
      selectedEmailDetail: null, // 当前选中邮件的详细数据
      detailLoading: false, // 邮件详情加载状态
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },
      showCreateCustomer: false,
      createAction: {
        type: 'save',
        id: '',
        data: {}
      },
      showDview: false,
      showCreateContact: false
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo
    }),

    // 获取客户邮箱地址
    customerEmailAddresses() {
      if (!this.detail) return []

      const emails = []

      // 从客户详情中获取邮箱地址
      if (this.detail.email) {
        emails.push(this.detail.email)
      }

      // 如果有其他邮箱字段，也可以添加
      if (this.detail.emailAddress) {
        emails.push(this.detail.emailAddress)
      }

      // 去重并过滤空值
      return [...new Set(emails.filter(email => email && email.trim()))]
    },
    currentEmailAddress() {
      if (this.detail.status == 'inbox') {
        return this.detail.sendEmailAddress
      } else if (this.detail.status == 'sent') {
        return this.detail.toList[0].emailAddress
      }
    },

    // 获取客户名称
    customerName() {
      return this.detail?.customerName || this.detail?.name || ''
    },

    // 获取客户ID
    customerId() {
      return this.id || this.detail?.customerId || this.detail?.id || ''
    },
  },
  methods: {
    isEmpty,
    extractUserName(email) {
      if (!email) return '';
      return email.split('@')[0];
    },
    // 处理邮件选择事件
    handleEmailSelected(email) {
      console.log('选中邮件:', email)
      this.selectedEmail = email
      this.drawer = true

      // 清空之前的详情数据
      this.selectedEmailDetail = null

      // 根据邮件ID获取详细信息
      this.getEmailDetails(email.id)
    },

    // 获取邮件详情
    async getEmailDetails(emailId) {
      if (!emailId) {
        console.warn('邮件ID为空，无法获取详情')
        this.$message.error('邮件ID为空')
        return
      }
      this.detailLoading = true

      try {
        const response = await queryEmailDetailsAPI({ id: emailId })

        // 设置邮件详情数据
        this.selectedEmailDetail = response.data

        // 标记邮件为已读（如果需要）
        this.markEmailAsRead(emailId)

      } catch (error) {
        console.error('获取邮件详情失败:', error)
        this.$message.error('获取邮件详情失败，请稍后重试')

        // 清空详情数据，避免显示错误的内容
        this.selectedEmailDetail = null
      } finally {
        this.detailLoading = false
      }
    },

    // 标记邮件为已读
    async markEmailAsRead(emailId) {
      try {
        await saveEmailReadStatusAPI(emailId)
        console.log('邮件已标记为已读:', emailId)

        // 更新列表中的邮件状态
        if (this.selectedEmail) {
          this.selectedEmail.flagsSeen = true
        }
      } catch (error) {
        console.error('标记邮件已读失败:', error)
        // 这里不显示错误消息，因为不影响主要功能
      }
    },

    // 关闭drawer
    handleClose(done) {
      this.drawer = false
      this.selectedEmail = null
      this.selectedEmailDetail = null
      this.detailLoading = false
      if (done) {
        done()
      }
    },

    addNewCustomer() {
      this.createAction = {
        type: 'save',
        id: '',
        data: {
          email: this.currentEmailAddress,
        }
      }
      this.showCreateCustomer = true
      // 通知父组件隐藏tooltip
      this.$emit('hide-tooltip')
    },
    addNewContact() {
      // 预填充邮箱信息到联系人创建表单
      this.createAction = {
        type: 'save',
        id: '',
        data: {
          // 预填充邮箱字段
          email: this.currentEmailAddress,
          // 预填充联系人姓名字段（使用邮箱@前缀作为姓名）
          name: this.extractUserName(this.currentEmailAddress)
        }
      }
      this.showCreateContact = true
      // 通知父组件隐藏tooltip
      this.$emit('hide-tooltip')
    },
    // 显示更多联系人信息
    showMoreInfo() {
      this.showDview = true;
    },

    closeCreateCustomer() {
      this.showCreateCustomer = false
    },
    closeCreateContact() {
      this.showCreateContact = false
    },
    handleCreateSuccess(data) {
      console.log("有调用这里吗", data);
      this.showCreateCustomer = false;
    },
    handleContactCreateSuccess(data) {
      console.log("联系人创建成功", data);
      this.showCreateContact = false;
    },

    // 邮件操作事件处理
    handleReply(email) {
      console.log('回复邮件:', email)
      this.$message.info('回复功能开发中...')
    },

    handleReplyAll(email) {
      console.log('回复全部:', email)
      this.$message.info('回复全部功能开发中...')
    },

    handleForward(email) {
      console.log('转发邮件:', email)
      this.$message.info('转发功能开发中...')
    },

    handleArchive(email) {
      console.log('归档邮件:', email)
      this.$message.info('归档功能开发中...')
    },

    handleTag(email) {
      console.log('标记邮件:', email)
      this.$message.info('标记功能开发中...')
    },

    handleDistribute(email) {
      console.log('分发邮件:', email)
      this.$message.info('分发功能开发中...')
    },

    handleStar(email) {
      console.log('星标邮件:', email)
      this.$message.info('星标功能开发中...')
    },

    handleDelete(email) {
      console.log('删除邮件:', email)
      this.$message.info('删除功能开发中...')
    },

    handleSetReminder(email) {
      console.log('设置提醒:', email)
      this.$message.info('提醒功能开发中...')
    },

    handleNavigate(direction) {
      console.log('导航:', direction)
      this.$message.info('导航功能开发中...')
    },

    handleFullscreen(email) {
      console.log('全屏查看:', email)
      this.$message.info('全屏功能开发中...')
    },

    handleAddNewClues(email) {
      console.log('添加线索:', email)
      this.$message.info('添加线索功能开发中...')
    },

    handleAddSalesOrder(email) {
      console.log('添加销售订单:', email)
      this.$message.info('添加销售订单功能开发中...')
    },

    handleViewNewTab(email) {
      console.log('新标签页查看:', email)
      this.$message.info('新标签页功能开发中...')
    },

    handleEditDraft(email) {
      console.log('编辑草稿:', email)
      this.$message.info('编辑草稿功能开发中...')
    },

    handleRemoveTag(email, tagId) {
      console.log('移除标签:', email, tagId)
      this.$message.info('移除标签功能开发中...')
    },

    handleRemoveFromArchive(email) {
      console.log('从归档中移除:', email)
      this.$message.info('移除归档功能开发中...')
    },

    // 从邮箱地址提取用户名
    extractUserName(email) {
      if (!email) return ''
      return email.split('@')[0]
    },
    // 处理分页变化
    handlePageChange({ currentPage, pageSize }) {
      console.log('分页变化:', { currentPage, pageSize })
      this.pagination.currentPage = currentPage
      this.pagination.pageSize = pageSize
      this.fetchData()
    },

    // 获取数据
    fetchData() {
      // 构建查询参数
      const params = {
        current: this.pagination.currentPage,
        size: this.pagination.pageSize,
        condition: {}
      }

      if (this.detail && this.detail.status == 'inbox') {
        params.condition = {
          emailAddress: '',
          customerId: '',
          emailAddressList: [this.detail.sendEmailAddress]
        }
      } else if (this.detail && this.detail.status == 'sent') {
        params.condition = {
          emailAddress: '',
          customerId: '',
          emailAddressList: this.detail.toList.map(item => item.emailAddress)
        }
      } else {
        params.condition = {
          emailAddress: '',
          customerId: this.customerId,
          emailAddressList: []
        }
      }

      getCustomerMailsAPI(params)
        .then(res => {
          this.tableData = res.data.records || []
          this.tableData = this.tableData.map(email => ({
            ...email,
            sender: email.sendEmailAddress || '',
            time: email.sentTime || email.receivedTime,
            isStarred: email.isStarred || false,
            read: email.flagsSeen || false,
            subject: email.subject || '',
            content: email.content || '',
            receivedAddress: (email.toList && email.toList.length > 0) ? email.toList[0].emailAddress : '',
            cc: email.ccList || [],
            bcc: email.bccList || [],
            attachments: email.fileList || [],
            hasAttachment: email.fileList && email.fileList.length > 0,
            size: email.size || 0,
            folder: email.folder || '',
            receivedDate: email.receivedTime || '',
            sendDate: email.sentTime || '',
            customerId: email.customerId || null,
            showTrackDetailTips: false
          }))

          this.pagination.total = parseInt(res.data.total) || 0

          // 如果没有数据且有客户信息，显示提示
          if (this.tableData.length === 0 && this.customerName) {
            console.log(`未找到客户 ${this.customerName} 的往来邮件`)
          }
        })
        .catch(error => {
          console.error('获取邮件列表失败:', error)
          this.tableData = []
          this.pagination.total = 0
        })
    },

    // 初始化数据
    initData() {
      // 如果有客户信息，则获取对应的邮件数据
      if (this.customerId || this.customerEmailAddresses.length > 0 || this.detail) {
        this.fetchData()
      } else {
        console.log('没有客户信息，不加载邮件数据')
      }
    }
  },
  mounted() {
    this.initData()
  },
  watch: {
    // 监听客户数据变化，重新加载邮件数据
    detail: {
      handler() {
        this.initData()
      },
      deep: true
    },
    // 监听客户ID变化
    id() {
      this.initData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/views/crm/styles/detailview.scss";
.mail-page {
  padding: 0 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

/* 联系人信息区域样式 */
.contact-info-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.contact-card {
  background: #fff;
  border: 1px solid #e6e9ed;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.contact-avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: #b8c5d1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  border: 4px solid #e8eef5;
}

.contact-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  width: 100%;
  height: 100%;
  background: #b8c5d1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-silhouette {
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: relative;
}

.avatar-silhouette::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #b8c5d1;
  border-radius: 50%;
}

.avatar-silhouette::after {
  content: '';
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 35px;
  height: 25px;
  background: #b8c5d1;
  border-radius: 50% 50% 0 0;
}

.contact-details {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-name {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.vip-badge {
  background: #f56c6c;
  color: #fff;
  font-size: 12px;
  font-weight: normal;
  padding: 4px 8px;
  border-radius: 4px;
  line-height: 1;
  border: 1px solid #f56c6c;
}

.contact-email,
.contact-company {
  font-size: 16px;
  color: #909399;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
}

.contact-email i,
.contact-company i {
  color: #909399;
  font-size: 16px;
}

.contact-actions {
  top: 20px;
  right: 20px;
}

.contact-actions .el-button {
  color: #409eff;
  font-size: 14px;
  padding: 8px 16px;
}

.contact-actions .el-button:hover {
  color: #66b1ff;
  background: #f0f9ff;
}

/* 联系人未找到样式 */
.contact-not-found {
  padding: 20px;
}

.warning-icon {
  margin-bottom: 20px;
}

.warning-icon i {
  font-size: 48px;
  color: #e6a23c;
  background: #fdf6ec;
  border: 3px solid #f5dab1;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.contact-email-link {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.email-link {
  color: #409eff;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
}

.email-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.email-icon {
  color: #409eff;
  font-size: 16px;
}

.not-found-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.action-message {
  font-size: 16px;
  color: #606266;
  margin-bottom: 24px;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.action-buttons .el-button {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  min-width: 120px;
}

.action-buttons .el-button--danger {
  background: #0052CC;
  border-color: #0052CC;
  color: #fff;
}

.action-buttons .el-button--danger:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 确保EmailList组件能够正确占用空间 */
.mail-page>* {
  flex-shrink: 0;
}

/* EmailList组件占用剩余空间 */
.mail-page :deep(.email-list) {
  flex: 1;
  min-height: 0;
  height: calc(100vh - 140px);
  /* 减去页面padding和联系人信息区域高度 */
  max-height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

/* 确保邮件列表区域可以滚动 */
.mail-page :deep(.email-group) {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: calc(100% - 70px);
  /* 减去分页高度 */
  max-height: calc(100% - 70px);
}

/* 确保分页固定在底部 */
.mail-page :deep(.email-pagination) {
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  height: 70px;
  min-height: 70px;
  max-height: 70px;
}

/* 客户邮件列表特定样式 */
.customer-email-list {
  height: 100%;
  max-height: calc(100vh - 140px);
  /* 减去页面padding和联系人信息区域高度 */
}

.customer-email-list :deep(.email-group) {
  height: calc(100% - 70px);
  /* 减去分页高度 */
  max-height: calc(100% - 70px);
  border: 1px solid #e6e9ed;
  border-radius: 4px;
  background-color: #fff;
}

.customer-email-list :deep(.email-pagination) {
  background-color: #fafafa;
  border-top: 2px solid #409eff;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
}

/* 无数据状态优化 */
.customer-email-list :deep(.no-results) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
  font-size: 14px;
}

.customer-email-list :deep(.no-results::before) {
  content: '📧';
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.filter-form {
  margin-bottom: 20px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #606266;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner i {
  font-size: 32px;
  color: #409eff;
  animation: rotate 2s linear infinite;
}

.loading-spinner span {
  font-size: 14px;
  color: #909399;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 无邮件选中状态样式 */
.no-email-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  text-align: center;
}

.no-email-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-email-text {
  font-size: 16px;
  color: #909399;
}

/* Drawer 内容样式优化 */
:deep(.el-drawer__body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mail-page {
    padding: 10px;
  }

  :deep(.el-drawer) {
    width: 90% !important;
  }

  .contact-avatar {
    width: 100px;
    height: 100px;
    margin-bottom: 16px;
  }

  .avatar-silhouette {
    width: 50px;
    height: 50px;
  }

  .avatar-silhouette::before {
    width: 16px;
    height: 16px;
    top: 12px;
  }

  .avatar-silhouette::after {
    width: 28px;
    height: 20px;
    bottom: 6px;
  }

  .contact-name {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .contact-email,
  .contact-company {
    font-size: 14px;
  }

  .contact-actions {
    position: static;
    margin-top: 16px;
  }

  .contact-not-found {
    padding: 30px 16px;
  }

  .warning-icon i {
    width: 60px;
    height: 60px;
    font-size: 36px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons .el-button {
    width: 100%;
    min-width: auto;
  }
}
</style>
