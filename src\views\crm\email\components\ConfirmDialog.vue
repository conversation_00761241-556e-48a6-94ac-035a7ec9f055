<template>
  <div class="confirm-dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="confirm-dialog" @click.stop>
      <div class="confirm-dialog-header">
        <h3>{{ title }}</h3>
        <button class="close-dialog" @click="cancel">
          <x-icon class="icon-small" />
        </button>
      </div>
      <div class="confirm-dialog-body">
        <p>{{ message }}</p>
      </div>
      <div class="confirm-dialog-footer">
        <button class="cancel-btn" @click="cancel">{{ cancelText }}</button>
        <button class="confirm-btn" :class="{ 'danger': isDanger }" @click="confirm">{{ confirmText }}</button>
      </div>
    </div>
  </div>
</template>

<script>
import { XIcon } from 'lucide-vue'

export default {
  name: 'ConfirmDialog',
  components: {
    XIcon
  },
  props: {
    visible: {
      type: <PERSON>olean,
      default: false
    },
    title: {
      type: String,
      default: '确认'
    },
    message: {
      type: String,
      required: true
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    isDanger: {
      type: Boolean,
      default: false
    },
    closeOnOverlayClick: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    confirm() {
      this.$emit('confirm');
    },
    cancel() {
      this.$emit('cancel');
    },
    handleOverlayClick() {
      if (this.closeOnOverlayClick) {
        this.cancel();
      }
    }
  }
}
</script>

<style scoped>
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 90vw;
  overflow: hidden;
}

.confirm-dialog-header {
  padding: 16px;
  border-bottom: 1px solid #e6e9ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.confirm-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-dialog {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
}

/* 确保SVG图标正确显示 */
::v-deep svg {
  width: 16px;
  height: 16px;
  display: inline-block;
  flex-shrink: 0;
  color: inherit;
}

.confirm-dialog-body {
  padding: 20px 16px;
  font-size: 14px;
  color: #333;
}

.confirm-dialog-footer {
  padding: 12px 16px;
  border-top: 1px solid #e6e9ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn, .confirm-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
}

.cancel-btn {
  background-color: #fff;
  color: #333;
}

.confirm-btn {
  background-color: #0052CC;
  color: white;
  border-color: #0052CC;
}

.confirm-btn.danger {
  background-color: #e60012;
  border-color: #e60012;
}

.cancel-btn:hover {
  background-color: #f5f5f5;
}

.confirm-btn:hover {
  opacity: 0.9;
}
</style>
